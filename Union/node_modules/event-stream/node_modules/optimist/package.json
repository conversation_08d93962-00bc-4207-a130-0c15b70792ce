{"name": "optimist", "version": "0.2.8", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1.0"}, "devDependencies": {"hashish": "0.0.x", "expresso": "0.7.x"}, "scripts": {"test": "expresso"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}}