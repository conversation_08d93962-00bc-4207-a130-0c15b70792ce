{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src.ts/utils/errors.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAMH,OAAO,KAAK,EACR,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,EAC9D,MAAM,uBAAuB,CAAC;AAE/B,OAAO,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAE9D;;;GAGG;AACH,MAAM,MAAM,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,cAAc,CAAC,GAAG;IAAE,YAAY,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AA+C7G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4DG;AACH,MAAM,MAAM,SAAS,GAGjB,eAAe,GAAG,iBAAiB,GAAG,uBAAuB,GAC7D,eAAe,GAAG,cAAc,GAAG,SAAS,GAAG,UAAU,GACzD,WAAW,GAGX,gBAAgB,GAAI,eAAe,GAGnC,kBAAkB,GAAG,kBAAkB,GAAG,qBAAqB,GAC/D,gBAAgB,GAGhB,gBAAgB,GAAG,oBAAoB,GAAG,eAAe,GACzD,yBAAyB,GAAG,sBAAsB,GAClD,mBAAmB,GAAG,gBAAgB,GAGtC,iBAAiB,CACpB;AAED;;;GAGG;AACH,MAAM,WAAW,WAAW,CAAC,CAAC,SAAS,SAAS,GAAG,SAAS,CAAE,SAAQ,KAAK;IACvE;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAEhB;;;OAGG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAE3B;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC;CACjB;AAID;;;GAGG;AACH,MAAM,WAAW,YAAa,SAAQ,WAAW,CAAC,eAAe,CAAC;IAC9D,CAAE,GAAG,EAAE,MAAM,GAAI,GAAG,CAAC;CACxB;AAED;;;GAGG;AACH,MAAM,WAAW,mBAAoB,SAAQ,WAAW,CAAC,iBAAiB,CAAC;IACvE;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;CACrB;AAED;;;;;;;;;GASG;AACH,MAAM,WAAW,yBAA0B,SAAQ,WAAW,CAAC,uBAAuB,CAAC;IACnF;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,WAAW,CAAC,eAAe,CAAC;IAC9D;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;CACjB;AAED;;;GAGG;AACH,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,cAAc,CAAC;IAC5D;;OAEG;IACH,OAAO,EAAE,YAAY,GAAG,MAAM,CAAC;IAE/B;;OAEG;IACH,QAAQ,CAAC,EAAE,aAAa,CAAC;CAC5B;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,YAAa,SAAQ,WAAW,CAAC,SAAS,CAAC;IACxD;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,OAAO,CAAC,EAAE,YAAY,CAAC;CAC1B;AAED;;;GAGG;AACH,MAAM,WAAW,YAAa,SAAQ,WAAW,CAAC,UAAU,CAAC;IACzD;;OAEG;IACH,KAAK,EAAE,GAAG,CAAC;CACd;AAED;;;GAGG;AACH,MAAM,WAAW,cAAe,SAAQ,WAAW,CAAC,WAAW,CAAC;CAC/D;AAKD;;;;;;GAMG;AACH,MAAM,WAAW,kBAAmB,SAAQ,WAAW,CAAC,gBAAgB,CAAC;IACrE;;OAEG;IACH,MAAM,EAAE,UAAU,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;CAClB;AAED;;;;;;GAMG;AACH,MAAM,WAAW,iBAAkB,SAAQ,WAAW,CAAC,eAAe,CAAC;IACnE;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,KAAK,EAAE,GAAG,CAAC;CACd;AAKD;;;GAGG;AACH,MAAM,WAAW,oBAAqB,SAAQ,WAAW,CAAC,kBAAkB,CAAC;IACzE;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,KAAK,EAAE,GAAG,CAAC;IAEX,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,oBAAqB,SAAQ,WAAW,CAAC,kBAAkB,CAAC;IACzE;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,WAAW,CAAC,qBAAqB,CAAC;IAC/E;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACzB;AAKD;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG,MAAM,GAAG,aAAa,GAAG,sBAAsB,GAAG,iBAAiB,GAAG,SAAS,CAAC;AAElH;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACnC,EAAE,EAAE,IAAI,GAAG,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,kBAAmB,SAAQ,WAAW,CAAC,gBAAgB,CAAC;IAErE;;OAEG;IACH,MAAM,EAAE,mBAAmB,CAAC;IAE5B;;OAEG;IACH,IAAI,EAAE,IAAI,GAAG,MAAM,CAAC;IAEpB;;OAEG;IACH,MAAM,EAAE,IAAI,GAAG,MAAM,CAAC;IAEtB;;OAEG;IACH,WAAW,EAAE,wBAAwB,CAAC;IAEtC;;OAEG;IACH,UAAU,EAAE,IAAI,GAAG;QACf,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,EAAE,MAAM,CAAC;QAClB,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;KACpB,CAAA;IAED;;OAEG;IACH,MAAM,EAAE,IAAI,GAAG;QACX,SAAS,EAAE,MAAM,CAAC;QAClB,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;KACpB,CAAA;IAED;;;OAGG;IACH,OAAO,CAAC,EAAE,kBAAkB,CAAC;CAChC;AAGD;;;GAGG;AACH,MAAM,WAAW,sBAAuB,SAAQ,WAAW,CAAC,oBAAoB,CAAC;IAC7E;;OAEG;IACH,WAAW,EAAE,kBAAkB,CAAC;CACnC;AAED;;;GAGG;AACH,MAAM,WAAW,iBAAkB,SAAQ,WAAW,CAAC,eAAe,CAAC;IACnE;;OAEG;IACH,WAAW,EAAE,kBAAkB,CAAC;CACnC;AAED;;;GAGG;AACH,MAAM,WAAW,kBAAmB,SAAQ,WAAW,CAAC,gBAAgB,CAAC;IACrE;;OAEG;IACH,WAAW,CAAC,EAAE,kBAAkB,CAAC;IAEjC;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;CAClB;AAED;;;;GAIG;AACH,MAAM,WAAW,2BAA4B,SAAQ,WAAW,CAAC,yBAAyB,CAAC;IACvF;;OAEG;IACH,WAAW,EAAE,kBAAkB,CAAC;CACnC;AAED;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,WAAW,CAAC,sBAAsB,CAAC;IACjF;;;OAGG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAE,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;IAE9C;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,WAAW,EAAE,mBAAmB,CAAC;IAEjC;;OAEG;IACH,OAAO,EAAE,kBAAkB,CAAC;CAC/B;AAED;;;;;;GAMG;AACH,MAAM,WAAW,qBAAsB,SAAQ,WAAW,CAAC,mBAAmB,CAAC;IAC3E;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;CACjB;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,mBAAoB,SAAQ,WAAW,CAAC,iBAAiB,CAAC;IACvE;;OAEG;IACH,MAAM,EAAE,eAAe,GAAG,iBAAiB,GAAG,aAAa,GAAG,iBAAiB,GAAG,eAAe,GAAG,SAAS,CAAC;IAE9G;;;;;;OAMG;IACH,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,SAAS,CAAA;CAC7C;AAID;;;;;GAKG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAC1B,CAAC,SAAS,eAAe,GAAG,YAAY,GACxC,CAAC,SAAS,iBAAiB,GAAG,mBAAmB,GACjD,CAAC,SAAS,uBAAuB,GAAG,yBAAyB,GAC7D,CAAC,SAAS,eAAe,GAAG,YAAY,GACxC,CAAC,SAAS,cAAc,GAAG,WAAW,GACtC,CAAC,SAAS,SAAS,GAAG,YAAY,GAClC,CAAC,SAAS,UAAU,GAAG,YAAY,GACnC,CAAC,SAAS,WAAW,GAAG,cAAc,GAEtC,CAAC,SAAS,gBAAgB,GAAG,kBAAkB,GAC/C,CAAC,SAAS,eAAe,GAAG,iBAAiB,GAE7C,CAAC,SAAS,kBAAkB,GAAG,oBAAoB,GACnD,CAAC,SAAS,kBAAkB,GAAG,oBAAoB,GACnD,CAAC,SAAS,qBAAqB,GAAG,uBAAuB,GAEzD,CAAC,SAAS,gBAAgB,GAAG,kBAAkB,GAC/C,CAAC,SAAS,oBAAoB,GAAG,sBAAsB,GACvD,CAAC,SAAS,eAAe,GAAG,iBAAiB,GAC7C,CAAC,SAAS,gBAAgB,GAAG,kBAAkB,GAC/C,CAAC,SAAS,yBAAyB,GAAG,2BAA2B,GACjE,CAAC,SAAS,sBAAsB,GAAG,wBAAwB,GAC3D,CAAC,SAAS,mBAAmB,GAAG,qBAAqB,GAErD,CAAC,SAAS,iBAAiB,GAAG,mBAAmB,GAEjD,KAAK,CAAC;AAIV;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,SAAS,EAAE,CAAC,SAAS,gBAAgB,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,CAE3G;AAED;;GAEG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,kBAAkB,CAEvE;AAED;;;;;;;;;GASG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,SAAS,EAAE,CAAC,SAAS,gBAAgB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAkD9H;AAED;;;;;GAKG;AACH,wBAAgB,MAAM,CAAC,CAAC,SAAS,SAAS,EAAE,CAAC,SAAS,gBAAgB,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAEvJ;AAGD;;;;;;GAMG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,KAAK,CAE3G;AAED,wBAAgB,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAahG;AAuBD;;GAEG;AACH,wBAAgB,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAIlD;AAED;;;;;GAKG;AACH,wBAAgB,aAAa,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAYnF"}