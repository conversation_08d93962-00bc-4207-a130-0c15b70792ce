{"version": 3, "file": "interface.d.ts", "sourceRoot": "", "sources": ["../../src.ts/abi/interface.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;GAUG;AAUH,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,4BAA4B,CAAC;AACvE,OAAO,EACH,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,EACnE,QAAQ,EAAE,gBAAgB,EAAE,SAAS,EACxC,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAEnC,OAAO,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAE/G,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAGnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC;AAErC;;;GAGG;AACH,qBAAa,cAAc;IACvB;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAG,aAAa,CAAC;IAElC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAEvB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAE5B;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAG,MAAM,CAAC;IAExB;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAA;IAEtB;;OAEG;gBACS,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;CAMnE;AAED;;;;GAIG;AACH,qBAAa,sBAAsB;IAC/B;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAG,gBAAgB,CAAC;IAErC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAEvB;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAEvB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAE5B;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAG,MAAM,CAAC;IAE3B;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAG,MAAM,CAAC;IAExB;;OAEG;gBACS,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;CAMxF;AAED;;;GAGG;AACH,qBAAa,gBAAgB;IACzB;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAG,aAAa,CAAC;IAElC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAEvB;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAEvB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAE5B;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAG,MAAM,CAAC;IAE3B;;OAEG;gBACS,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;CAMtE;AAED;;;;;GAKG;AACH,qBAAa,OAAO;IAChB;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,IAAI,GAAG,MAAM,CAAC;IAE9B;;OAEG;IACH,QAAQ,CAAC,UAAU,EAAG,OAAO,CAAC;IAE9B;;;;OAIG;IACH,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,OAAO;IAI9C;;OAEG;gBACS,IAAI,EAAE,IAAI,GAAG,MAAM;CAGlC;AAmED;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,MAAM,YAAY,GAAG,MAAM,GAAG,aAAa,CAAC,QAAQ,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC;AAEpF;;;;;;;;;GASG;AACH,qBAAa,SAAS;;IAElB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;IAE7C;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAG,mBAAmB,CAAC;IAEtC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAG,IAAI,GAAG,gBAAgB,CAAC;IAE5C;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAG,OAAO,CAAC;IAS3B;;OAEG;gBACS,SAAS,EAAE,YAAY;IA4FnC;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;IAMxC;;;OAGG;IACH,UAAU,IAAI,MAAM;IAOpB;;;OAGG;IACH,WAAW,IAAI,QAAQ;IA2FvB;;;OAGG;IACH,eAAe,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAMpC;;;;;;OAMG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAIjC;;;;;;;;;OASG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,gBAAgB;IAI9E;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IAsEhF;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAOjC;;;;;;OAMG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAI9B;;;;;;;;;OASG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,aAAa;IAIxE;;OAEG;IACH,YAAY,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IAS1E;;;;;;;;;OASG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,aAAa;IA6CxE;;OAEG;IACH,YAAY,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IAwC1E,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAIxE,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAInF;;;OAGG;IACH,YAAY,CAAC,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAIjD;;;;;;;;OAQG;IACH,iBAAiB,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAa5E;;;;;;;OAOG;IACH,iBAAiB,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAaxF;;;;;;;OAOG;IACH,kBAAkB,CAAC,QAAQ,EAAE,gBAAgB,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAahF;;;;OAIG;IACH,kBAAkB,CAAC,QAAQ,EAAE,gBAAgB,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAa5F;;;;;;;;OAQG;IACH,oBAAoB,CAAC,QAAQ,EAAE,gBAAgB,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAyBlF,SAAS,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,wBAAwB,GAAG,kBAAkB;IAsC7E;;;;;;;OAOG;IACH,oBAAoB,CAAC,QAAQ,EAAE,gBAAgB,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAuC9F,kBAAkB,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAgEtH,cAAc,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;KAAE;IA6CrH,cAAc,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM;IAuEzG;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,KAAK,CAAC,EAAE,YAAY,CAAA;KAAE,GAAG,IAAI,GAAG,sBAAsB;IAY3F,eAAe,CAAC,IAAI,EAAE,SAAS,GAAG,MAAM;IAIxC;;;;;OAKG;IACH,QAAQ,CAAC,GAAG,EAAE;QAAE,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI,GAAG,cAAc;IAapF;;;;;OAKG;IACH,UAAU,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,GAAG,gBAAgB;IAWpD;;;;;OAKG;IACH,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS,GAAG,SAAS;CAoB1D"}