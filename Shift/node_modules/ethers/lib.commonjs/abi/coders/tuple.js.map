{"version": 3, "file": "tuple.js", "sourceRoot": "", "sources": ["../../../src.ts/abi/coders/tuple.ts"], "names": [], "mappings": ";;;AAAA,6DAA6D;AAE7D,0CAAoC;AACpC,2DAA4C;AAE5C,yCAA0C;AAI1C;;GAEG;AACH,MAAa,UAAW,SAAQ,yBAAK;IACxB,MAAM,CAAwB;IAEvC,YAAY,MAAoB,EAAE,SAAiB;QAC/C,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,KAAK,GAAkB,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACrB,IAAI,KAAK,CAAC,OAAO,EAAE;gBAAE,OAAO,GAAG,IAAI,CAAC;aAAE;YACtC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QAEhD,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACzC,IAAA,gCAAgB,EAAa,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IAClF,CAAC;IAED,YAAY;QACR,MAAM,MAAM,GAAQ,EAAG,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,4DAA4D;QAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC7B,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAAE;gBACtC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;aACjB;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,EAAgC,EAAG,CAAC,CAAC;QAEtC,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAY,EAAE,KAAa,EAAE,EAAE;YAChD,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC3B,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAAE,OAAO;aAAE;YAEjD,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAAE,IAAI,GAAG,SAAS,CAAC;aAAE;YAE5C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;gBAAE,OAAO;aAAE;YAErC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,MAAsD;QACzE,MAAM,KAAK,GAAG,gBAAK,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACjD,OAAO,IAAA,eAAI,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,OAAO,IAAA,iBAAM,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;CACJ;AAvDD,gCAuDC"}