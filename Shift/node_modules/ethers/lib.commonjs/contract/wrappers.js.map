{"version": 3, "file": "wrappers.js", "sourceRoot": "", "sources": ["../../src.ts/contract/wrappers.ts"], "names": [], "mappings": ";;;AAAA,sEAAsE;AACtE,yBAAyB;AACzB,0DAEkC;AAClC,gDAAmE;AAWnE;;GAEG;AACH,MAAa,QAAS,SAAQ,iBAAG;IAC7B;;OAEG;IACM,SAAS,CAAa;IAE/B;;OAEG;IACM,QAAQ,CAAiB;IAElC;;OAEG;IACM,IAAI,CAAU;IAEvB;;OAEG;IACH,YAAY,GAAQ,EAAE,KAAgB,EAAE,QAAuB;QAC3D,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzB,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAClE,IAAA,2BAAgB,EAAW,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,IAAI,SAAS,KAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAEtD;;OAEG;IACH,IAAI,cAAc,KAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;CAClE;AAlCD,4BAkCC;AAED;;GAEG;AACH,MAAa,iBAAkB,SAAQ,iBAAG;IAEtC;;OAEG;IACM,KAAK,CAAS;IAEvB;;OAEG;IACH,YAAY,GAAQ,EAAE,KAAY;QAC9B,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzB,IAAA,2BAAgB,EAAoB,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;IACzD,CAAC;CACJ;AAdD,8CAcC;AAED;;;GAGG;AACH,MAAa,0BAA2B,SAAQ,gCAAkB;IACrD,MAAM,CAAY;IAE3B;;OAEG;IACH,YAAY,KAAgB,EAAE,QAAkB,EAAE,EAAsB;QACpE,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,IAAI,IAAI;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YAC1B,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,CAAC,IAAI,CAAC;YAC/E,IAAI,QAAQ,EAAE;gBACV,IAAI;oBACA,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;iBAClD;gBAAC,OAAO,KAAU,EAAE;oBACjB,OAAO,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAC5C;aACJ;YAED,OAAO,GAAG,CAAC;QACf,CAAC,CAAC,CAAC;IACP,CAAC;CAEJ;AA9BD,gEA8BC;AAED;;;GAGG;AACH,MAAa,2BAA4B,SAAQ,iCAAmB;IACvD,MAAM,CAAY;IAE3B;;OAEG;IACH,YAAY,KAAgB,EAAE,QAAkB,EAAE,EAAuB;QACrE,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,IAAI,CAAC,QAAiB,EAAE,OAAgB;QAC1C,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QACrC,OAAO,IAAI,0BAA0B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC/E,CAAC;CACJ;AAzBD,kEAyBC;AAED;;;GAGG;AACH,MAAc,2BAA4B,SAAQ,uBAA+B;IAC7E;;OAEG;IACM,GAAG,CAAO;IAEnB;;OAEG;IACH,YAAY,QAAsB,EAAE,QAAyB,EAAE,MAAyB,EAAE,GAAQ;QAC9F,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAClC,IAAA,2BAAgB,EAA8B,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACV,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB;QACvB,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;IAClD,CAAC;CACJ;AAlCD,kEAkCC;AAED;;;GAGG;AACH,MAAa,oBAAqB,SAAQ,2BAA2B;IAiBjE;;OAEG;IACH,YAAY,QAAsB,EAAE,QAAyB,EAAE,MAAyB,EAAE,QAAuB,EAAE,IAAS;QACxH,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpF,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzF,IAAA,2BAAgB,EAAuB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IAClC,CAAC;CACJ;AAvCD,oDAuCC"}