import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import pty from 'node-pty';
import cors from 'cors';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import chalk from 'chalk';
import multer from 'multer';
import archiver from 'archiver';
import ProgramManager from './programManager.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend/dist')));

// Store active terminals
const terminals = new Map();
const terminalSessions = new Map();

// Initialize Program Manager
const programManager = new ProgramManager();
let programs = [];

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    fs.ensureDirSync(uploadDir);
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
    files: 10 // Max 10 files
  },
  fileFilter: (req, file, cb) => {
    // Allow all file types for now
    cb(null, true);
  }
});

// Helper function to get directory tree
async function getDirectoryTree(dirPath, relativePath = '') {
  const items = [];
  const entries = await fs.readdir(dirPath);

  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry);
    const relPath = path.join(relativePath, entry);
    const stats = await fs.stat(fullPath);

    if (stats.isDirectory()) {
      // Skip node_modules and other system directories
      if (['node_modules', '.git', '.vscode', 'venv', '__pycache__'].includes(entry)) {
        continue;
      }

      const children = await getDirectoryTree(fullPath, relPath);
      items.push({
        name: entry,
        path: relPath,
        type: 'directory',
        children
      });
    } else {
      items.push({
        name: entry,
        path: relPath,
        type: 'file',
        size: stats.size,
        modified: stats.mtime
      });
    }
  }

  return items.sort((a, b) => {
    // Directories first, then files, both alphabetically
    if (a.type !== b.type) {
      return a.type === 'directory' ? -1 : 1;
    }
    return a.name.localeCompare(b.name);
  });
}

// Helper function to scan for encryption/obfuscation
async function scanForEncryption(dirPath) {
  const suspiciousPatterns = [
    /eval\s*\(/gi,
    /Function\s*\(/gi,
    /atob\s*\(/gi,
    /btoa\s*\(/gi,
    /String\.fromCharCode/gi,
    /\\x[0-9a-f]{2}/gi,
    /\\u[0-9a-f]{4}/gi,
    /[a-zA-Z0-9+/]{50,}={0,2}/g, // Base64-like strings
    /var\s+_0x[a-f0-9]+/gi, // Common obfuscation pattern
    /_0x[a-f0-9]+\[/gi,
    /\['\\x[0-9a-f]{2}'\]/gi
  ];

  const encryptedFiles = [];
  const files = await fs.readdir(dirPath);

  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const stats = await fs.stat(filePath);

    if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.py') || file.endsWith('.ts'))) {
      try {
        const content = await fs.readFile(filePath, 'utf8');

        // Check for suspicious patterns
        const suspiciousCount = suspiciousPatterns.reduce((count, pattern) => {
          const matches = content.match(pattern);
          return count + (matches ? matches.length : 0);
        }, 0);

        // Check for high entropy (possible encryption)
        const entropy = calculateEntropy(content);

        if (suspiciousCount > 3 || entropy > 4.5) {
          encryptedFiles.push({
            file,
            suspiciousCount,
            entropy: entropy.toFixed(2),
            patterns: suspiciousPatterns.filter(pattern => pattern.test(content)).map(p => p.toString())
          });
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }
  }

  return encryptedFiles;
}

// Helper function to calculate entropy
function calculateEntropy(str) {
  const freq = {};
  for (let i = 0; i < str.length; i++) {
    freq[str[i]] = (freq[str[i]] || 0) + 1;
  }

  let entropy = 0;
  const len = str.length;
  for (const char in freq) {
    const p = freq[char] / len;
    entropy -= p * Math.log2(p);
  }

  return entropy;
}

// Initialize programs on startup
async function initializePrograms() {
  try {
    console.log(chalk.blue('🔍 Initializing programs...'));
    programs = await programManager.getAllPrograms();
    console.log(chalk.green(`✅ Loaded ${programs.length} programs`));
  } catch (error) {
    console.error(chalk.red('❌ Failed to initialize programs:'), error.message);
    programs = []; // Fallback to empty array
  }
}

// API Routes
app.get('/api/programs', async (req, res) => {
  try {
    // Refresh programs list
    programs = await programManager.getAllPrograms();
    res.json(programs);
  } catch (error) {
    console.error(chalk.red('❌ Error getting programs:'), error.message);
    res.status(500).json({ error: 'Failed to get programs' });
  }
});

// Add new program manually
app.post('/api/programs', async (req, res) => {
  try {
    const { name, directory, command, args, description, type, icon, color } = req.body;

    const newProgram = {
      id: programManager.generateId(directory),
      name: name || programManager.generateName(directory),
      description: description || `${name} automation tool`,
      directory,
      command: command || 'node',
      args: args || ['index.js'],
      type: type || 'application',
      icon: icon || '📦',
      color: color || '#4F46E5'
    };

    programs.push(newProgram);
    await programManager.savePrograms(programs);

    console.log(chalk.green(`✅ Added new program: ${newProgram.name}`));
    res.json(newProgram);
  } catch (error) {
    console.error(chalk.red('❌ Error adding program:'), error.message);
    res.status(500).json({ error: 'Failed to add program' });
  }
});

// Refresh programs (re-scan directories)
app.post('/api/programs/refresh', async (req, res) => {
  try {
    console.log(chalk.blue('🔄 Refreshing programs...'));
    programs = await programManager.getAllPrograms();
    console.log(chalk.green(`✅ Refreshed ${programs.length} programs`));
    res.json({ message: 'Programs refreshed', count: programs.length });
  } catch (error) {
    console.error(chalk.red('❌ Error refreshing programs:'), error.message);
    res.status(500).json({ error: 'Failed to refresh programs' });
  }
});

// Clone program from GitHub
app.post('/api/programs/clone', async (req, res) => {
  try {
    const { githubUrl, programName } = req.body;

    if (!githubUrl) {
      return res.status(400).json({ error: 'GitHub URL is required' });
    }

    if (!programName) {
      return res.status(400).json({ error: 'Program name is required' });
    }

    console.log(chalk.blue(`📥 Cloning from GitHub: ${githubUrl}`));

    // Create program directory
    const programDir = programName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const targetPath = path.join(__dirname, '../../', programDir);

    // Check if directory already exists
    if (await fs.pathExists(targetPath)) {
      return res.status(400).json({ error: 'Program directory already exists' });
    }

    // Clone the repository
    const { spawn } = await import('child_process');
    const gitProcess = spawn('git', ['clone', githubUrl, targetPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    gitProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    gitProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    gitProcess.on('close', async (code) => {
      if (code !== 0) {
        console.error(chalk.red(`❌ Git clone failed: ${errorOutput}`));
        return res.status(500).json({ error: `Git clone failed: ${errorOutput}` });
      }

      try {
        // Scan for encryption/obfuscation
        console.log(chalk.blue(`🔍 Scanning for encryption in ${programName}...`));
        const encryptedFiles = await scanForEncryption(targetPath);

        if (encryptedFiles.length > 0) {
          console.log(chalk.yellow(`⚠️ Found ${encryptedFiles.length} potentially encrypted/obfuscated files in ${programName}`));
          return res.status(200).json({
            success: false,
            encrypted: true,
            encryptedFiles,
            message: `Found ${encryptedFiles.length} potentially encrypted/obfuscated files`,
            directory: programDir,
            githubUrl
          });
        }

        // Setup Python environment if needed
        const files_in_dir = await fs.readdir(targetPath);
        if (files_in_dir.includes('requirements.txt')) {
          console.log(chalk.blue(`🐍 Setting up Python environment for ${programName}`));

          // Copy Python runner
          const runnerSource = path.join(__dirname, 'python-runner.sh');
          const runnerTarget = path.join(targetPath, 'run-python.sh');
          await fs.copy(runnerSource, runnerTarget);
          await fs.chmod(runnerTarget, '755');

          // Create setup script
          const setupScript = `#!/bin/bash
echo "🐍 Setting up Python environment for ${programName}..."
cd "${targetPath}"
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
echo "✅ Python environment ready!"
`;

          const setupPath = path.join(targetPath, 'setup-python.sh');
          await fs.writeFile(setupPath, setupScript);
          await fs.chmod(setupPath, '755');
        }

        // Install Node.js dependencies if needed
        if (files_in_dir.includes('package.json')) {
          console.log(chalk.blue(`📦 Installing Node.js dependencies for ${programName}`));
          const npmProcess = spawn('npm', ['install'], {
            cwd: targetPath,
            stdio: ['pipe', 'pipe', 'pipe']
          });

          npmProcess.on('close', async (npmCode) => {
            if (npmCode !== 0) {
              console.warn(chalk.yellow(`⚠️ npm install failed for ${programName}`));
            }

            // Refresh programs to detect the new one
            programs = await programManager.getAllPrograms();

            console.log(chalk.green(`✅ Program ${programName} cloned successfully from GitHub`));

            res.json({
              success: true,
              message: `Program ${programName} cloned successfully from GitHub`,
              directory: programDir,
              githubUrl
            });
          });
        } else {
          // Refresh programs to detect the new one
          programs = await programManager.getAllPrograms();

          console.log(chalk.green(`✅ Program ${programName} cloned successfully from GitHub`));

          res.json({
            success: true,
            message: `Program ${programName} cloned successfully from GitHub`,
            directory: programDir,
            githubUrl
          });
        }
      } catch (error) {
        console.error(chalk.red('❌ Error setting up cloned program:'), error.message);
        res.status(500).json({ error: error.message });
      }
    });

  } catch (error) {
    console.error(chalk.red('❌ Error cloning from GitHub:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Force clone program from GitHub (bypass encryption warning)
app.post('/api/programs/clone/force', async (req, res) => {
  try {
    const { githubUrl, programName } = req.body;

    if (!githubUrl) {
      return res.status(400).json({ error: 'GitHub URL is required' });
    }

    if (!programName) {
      return res.status(400).json({ error: 'Program name is required' });
    }

    console.log(chalk.yellow(`⚠️ Force cloning from GitHub (bypassing encryption check): ${githubUrl}`));

    // Create program directory
    const programDir = programName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const targetPath = path.join(__dirname, '../../', programDir);

    // Check if directory already exists
    if (await fs.pathExists(targetPath)) {
      return res.status(400).json({ error: 'Program directory already exists' });
    }

    // Clone the repository
    const { spawn } = await import('child_process');
    const gitProcess = spawn('git', ['clone', githubUrl, targetPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    gitProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    gitProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    gitProcess.on('close', async (code) => {
      if (code !== 0) {
        console.error(chalk.red(`❌ Git clone failed: ${errorOutput}`));
        return res.status(500).json({ error: `Git clone failed: ${errorOutput}` });
      }

      try {
        // Setup Python environment if needed
        const files_in_dir = await fs.readdir(targetPath);
        if (files_in_dir.includes('requirements.txt')) {
          console.log(chalk.blue(`🐍 Setting up Python environment for ${programName}`));

          // Copy Python runner
          const runnerSource = path.join(__dirname, 'python-runner.sh');
          const runnerTarget = path.join(targetPath, 'run-python.sh');
          await fs.copy(runnerSource, runnerTarget);
          await fs.chmod(runnerTarget, '755');

          // Create setup script
          const setupScript = `#!/bin/bash
echo "🐍 Setting up Python environment for ${programName}..."
cd "${targetPath}"
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
echo "✅ Python environment ready!"
`;

          const setupPath = path.join(targetPath, 'setup-python.sh');
          await fs.writeFile(setupPath, setupScript);
          await fs.chmod(setupPath, '755');
        }

        // Install Node.js dependencies if needed
        if (files_in_dir.includes('package.json')) {
          console.log(chalk.blue(`📦 Installing Node.js dependencies for ${programName}`));
          const npmProcess = spawn('npm', ['install'], {
            cwd: targetPath,
            stdio: ['pipe', 'pipe', 'pipe']
          });

          npmProcess.on('close', async (npmCode) => {
            if (npmCode !== 0) {
              console.warn(chalk.yellow(`⚠️ npm install failed for ${programName}`));
            }

            // Refresh programs to detect the new one
            programs = await programManager.getAllPrograms();

            console.log(chalk.green(`✅ Program ${programName} force cloned successfully from GitHub`));

            res.json({
              success: true,
              message: `Program ${programName} force cloned successfully from GitHub`,
              directory: programDir,
              githubUrl,
              warning: 'This program may contain encrypted/obfuscated code'
            });
          });
        } else {
          // Refresh programs to detect the new one
          programs = await programManager.getAllPrograms();

          console.log(chalk.green(`✅ Program ${programName} force cloned successfully from GitHub`));

          res.json({
            success: true,
            message: `Program ${programName} force cloned successfully from GitHub`,
            directory: programDir,
            githubUrl,
            warning: 'This program may contain encrypted/obfuscated code'
          });
        }
      } catch (error) {
        console.error(chalk.red('❌ Error setting up force cloned program:'), error.message);
        res.status(500).json({ error: error.message });
      }
    });

  } catch (error) {
    console.error(chalk.red('❌ Error force cloning from GitHub:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Delete program
app.delete('/api/programs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const program = programs.find(p => p.id === id);

    if (!program) {
      return res.status(404).json({ error: 'Program not found' });
    }

    console.log(chalk.yellow(`🗑️ Deleting program: ${program.name}`));

    // Stop any running terminals for this program
    const programTerminals = Array.from(terminalSessions.values())
      .filter(session => session.programId === id);

    for (const session of programTerminals) {
      try {
        if (session.terminal) {
          session.terminal.kill();
        }
        terminals.delete(session.id);
        terminalSessions.delete(session.id);
      } catch (error) {
        console.warn(chalk.yellow(`⚠️ Error stopping terminal for ${program.name}:`, error.message));
      }
    }

    // Remove program directory
    const programPath = path.join(__dirname, '../../', program.directory);
    if (await fs.pathExists(programPath)) {
      await fs.remove(programPath);
      console.log(chalk.green(`✅ Removed directory: ${programPath}`));
    }

    // Remove from programs list
    programs = programs.filter(p => p.id !== id);
    await programManager.savePrograms(programs);

    console.log(chalk.green(`✅ Program ${program.name} deleted successfully`));

    res.json({
      success: true,
      message: `Program ${program.name} deleted successfully`
    });

  } catch (error) {
    console.error(chalk.red('❌ Error deleting program:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Get program files
app.get('/api/programs/:id/files', async (req, res) => {
  try {
    const { id } = req.params;
    const program = programs.find(p => p.id === id);

    if (!program) {
      return res.status(404).json({ error: 'Program not found' });
    }

    const programPath = path.join(__dirname, '../../', program.directory);

    if (!await fs.pathExists(programPath)) {
      return res.status(404).json({ error: 'Program directory not found' });
    }

    const files = await getDirectoryTree(programPath);
    res.json(files);

  } catch (error) {
    console.error(chalk.red('❌ Error getting program files:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Get file content
app.get('/api/programs/:id/files/*', async (req, res) => {
  try {
    const { id } = req.params;
    const program = programs.find(p => p.id === id);

    if (!program) {
      return res.status(404).json({ error: 'Program not found' });
    }

    const filePath = req.params[0];
    const fullPath = path.join(__dirname, '../../', program.directory, filePath);
    const programPath = path.join(__dirname, '../../', program.directory);

    // Security check - ensure file is within program directory
    if (!fullPath.startsWith(programPath)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    if (!await fs.pathExists(fullPath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    const stats = await fs.stat(fullPath);
    if (stats.isDirectory()) {
      return res.status(400).json({ error: 'Path is a directory' });
    }

    // Check if file is binary
    const content = await fs.readFile(fullPath);
    const isBinary = content.includes(0);

    if (isBinary) {
      return res.json({
        isBinary: true,
        size: stats.size,
        message: 'Binary file cannot be edited'
      });
    }

    res.json({
      content: content.toString('utf8'),
      isBinary: false,
      size: stats.size
    });

  } catch (error) {
    console.error(chalk.red('❌ Error reading file:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Save file content
app.put('/api/programs/:id/files/*', async (req, res) => {
  try {
    const { id } = req.params;
    const program = programs.find(p => p.id === id);

    if (!program) {
      return res.status(404).json({ error: 'Program not found' });
    }

    const filePath = req.params[0];
    const { content } = req.body;
    const fullPath = path.join(__dirname, '../../', program.directory, filePath);
    const programPath = path.join(__dirname, '../../', program.directory);

    // Security check
    if (!fullPath.startsWith(programPath)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Create directory if it doesn't exist
    await fs.ensureDir(path.dirname(fullPath));

    // Save file
    await fs.writeFile(fullPath, content, 'utf8');

    console.log(chalk.green(`✅ File saved: ${filePath} in ${program.name}`));

    res.json({
      success: true,
      message: 'File saved successfully'
    });

  } catch (error) {
    console.error(chalk.red('❌ Error saving file:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Create new file
app.post('/api/programs/:id/files', async (req, res) => {
  try {
    const { id } = req.params;
    const program = programs.find(p => p.id === id);

    if (!program) {
      return res.status(404).json({ error: 'Program not found' });
    }

    const { filePath, content = '', isDirectory = false } = req.body;
    const fullPath = path.join(__dirname, '../../', program.directory, filePath);
    const programPath = path.join(__dirname, '../../', program.directory);

    // Security check
    if (!fullPath.startsWith(programPath)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    if (await fs.pathExists(fullPath)) {
      return res.status(400).json({ error: 'File or directory already exists' });
    }

    if (isDirectory) {
      await fs.ensureDir(fullPath);
      console.log(chalk.green(`✅ Directory created: ${filePath} in ${program.name}`));
    } else {
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content, 'utf8');
      console.log(chalk.green(`✅ File created: ${filePath} in ${program.name}`));
    }

    res.json({
      success: true,
      message: `${isDirectory ? 'Directory' : 'File'} created successfully`
    });

  } catch (error) {
    console.error(chalk.red('❌ Error creating file:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Delete file
app.delete('/api/programs/:id/files/*', async (req, res) => {
  try {
    const { id } = req.params;
    const program = programs.find(p => p.id === id);

    if (!program) {
      return res.status(404).json({ error: 'Program not found' });
    }

    const filePath = req.params[0];
    const fullPath = path.join(__dirname, '../../', program.directory, filePath);
    const programPath = path.join(__dirname, '../../', program.directory);

    // Security check
    if (!fullPath.startsWith(programPath)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    if (!await fs.pathExists(fullPath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    await fs.remove(fullPath);

    console.log(chalk.green(`✅ File deleted: ${filePath} in ${program.name}`));

    res.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error(chalk.red('❌ Error deleting file:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

// File upload endpoint
app.post('/api/programs/upload', upload.array('files'), async (req, res) => {
  try {
    const { programName, programType = 'application' } = req.body;
    const files = req.files;

    if (!files || files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    if (!programName) {
      return res.status(400).json({ error: 'Program name is required' });
    }

    console.log(chalk.blue(`📁 Processing upload for: ${programName}`));
    console.log(chalk.gray(`   Files: ${files.map(f => f.originalname).join(', ')}`));

    // Create program directory
    const programDir = programName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const targetPath = path.join(__dirname, '../../', programDir);

    await fs.ensureDir(targetPath);

    // Process uploaded files
    for (const file of files) {
      const sourcePath = file.path;
      const fileName = file.originalname;

      if (fileName.endsWith('.zip')) {
        // Extract zip file
        console.log(chalk.blue(`📦 Extracting: ${fileName}`));
        const extract = await import('extract-zip');
        await extract.default(sourcePath, { dir: targetPath });

        // Remove zip file after extraction
        await fs.remove(sourcePath);
      } else {
        // Copy regular file
        const targetFilePath = path.join(targetPath, fileName);
        await fs.move(sourcePath, targetFilePath);
      }
    }

    // Setup Python environment if needed
    const files_in_dir = await fs.readdir(targetPath);
    if (files_in_dir.includes('requirements.txt')) {
      console.log(chalk.blue(`🐍 Setting up Python environment for ${programName}`));

      // Copy Python runner
      const runnerSource = path.join(__dirname, 'python-runner.sh');
      const runnerTarget = path.join(targetPath, 'run-python.sh');
      await fs.copy(runnerSource, runnerTarget);
      await fs.chmod(runnerTarget, '755');

      // Create setup script
      const setupScript = `#!/bin/bash
echo "🐍 Setting up Python environment for ${programName}..."
cd "${targetPath}"
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
echo "✅ Python environment ready!"
`;

      const setupPath = path.join(targetPath, 'setup-python.sh');
      await fs.writeFile(setupPath, setupScript);
      await fs.chmod(setupPath, '755');
    }

    // Refresh programs to detect the new one
    programs = await programManager.getAllPrograms();

    console.log(chalk.green(`✅ Program ${programName} uploaded successfully`));

    res.json({
      success: true,
      message: `Program ${programName} uploaded successfully`,
      directory: programDir,
      files: files.map(f => f.originalname)
    });

  } catch (error) {
    console.error(chalk.red('❌ Error uploading program:'), error.message);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/terminals', (req, res) => {
  const activeTerminals = Array.from(terminalSessions.values()).map(session => ({
    id: session.id,
    programId: session.programId,
    programName: session.programName,
    status: session.status,
    startTime: session.startTime,
    pid: session.terminal?.pid
  }));
  res.json(activeTerminals);
});

app.post('/api/programs/:id/start', async (req, res) => {
  const { id } = req.params;
  const program = programs.find(p => p.id === id);

  if (!program) {
    return res.status(404).json({ error: 'Program not found' });
  }

  try {
    const terminalId = uuidv4();
    const programPath = path.join(__dirname, '../../', program.directory);

    console.log(chalk.blue(`🚀 Starting ${program.name}...`));
    console.log(chalk.gray(`   Directory: ${programPath}`));
    console.log(chalk.gray(`   Command: ${program.command} ${program.args.join(' ')}`));

    // Check if directory exists
    if (!await fs.pathExists(programPath)) {
      throw new Error(`Program directory not found: ${programPath}`);
    }

    // Handle Python programs with venv
    let command = program.command;
    let args = program.args;

    if (program.requiresVenv || command === './run-python.sh') {
      // Copy python runner script to program directory
      const runnerSource = path.join(__dirname, 'python-runner.sh');
      const runnerTarget = path.join(programPath, 'run-python.sh');

      if (await fs.pathExists(runnerSource)) {
        await fs.copy(runnerSource, runnerTarget);
        await fs.chmod(runnerTarget, '755');
        console.log(chalk.blue(`📋 Python runner copied to ${program.name}`));
      }
    }

    // Create PTY terminal
    const terminal = pty.spawn(command, args, {
      name: 'xterm-256color',
      cols: 120,
      rows: 30,
      cwd: programPath,
      env: {
        ...process.env,
        TERM: 'xterm-256color',
        COLORTERM: 'truecolor',
        FORCE_COLOR: '3',
        NODE_ENV: 'production',
        PYTHONUNBUFFERED: '1',
        PYTHONIOENCODING: 'utf-8'
      }
    });

    // Create session
    const session = {
      id: terminalId,
      programId: id,
      programName: program.name,
      terminal,
      status: 'running',
      startTime: new Date().toISOString(),
      history: []
    };

    terminals.set(terminalId, terminal);
    terminalSessions.set(terminalId, session);

    // Handle terminal data with enhanced logging and error handling
    terminal.on('data', (data) => {
      try {
        // Validate data
        if (data === undefined || data === null) {
          console.warn(chalk.yellow(`⚠️ Received invalid data from ${program.name}`));
          return;
        }

        // Store in session history
        session.history.push({
          type: 'output',
          data,
          timestamp: new Date().toISOString()
        });

        // Debug logging for data flow
        if (data.trim()) {
          console.log(chalk.cyan(`📤 Terminal data from ${program.name}: ${data.slice(0, 100)}${data.length > 100 ? '...' : ''}`));
        }

        // Emit to all connected clients
        io.emit('terminal-data', {
          terminalId,
          programId: id,
          programName: program.name,
          data,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error(chalk.red(`❌ Error handling terminal data from ${program.name}:`), error.message);
      }
    });

    // Handle terminal exit
    terminal.on('exit', (code, signal) => {
      console.log(chalk.yellow(`📋 ${program.name} exited with code ${code}`));

      session.status = 'stopped';
      session.exitCode = code;
      session.exitSignal = signal;
      session.endTime = new Date().toISOString();

      io.emit('terminal-exit', {
        terminalId,
        programId: id,
        programName: program.name,
        exitCode: code,
        exitSignal: signal,
        timestamp: new Date().toISOString()
      });

      // Clean up after 5 minutes
      setTimeout(() => {
        terminals.delete(terminalId);
        terminalSessions.delete(terminalId);
      }, 5 * 60 * 1000);
    });

    console.log(chalk.green(`✅ ${program.name} started with terminal ID: ${terminalId}`));

    res.json({
      success: true,
      terminalId,
      programId: id,
      programName: program.name,
      pid: terminal.pid
    });

  } catch (error) {
    console.error(chalk.red(`❌ Failed to start ${program.name}:`), error.message);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/terminals/:id/input', (req, res) => {
  const { id } = req.params;
  const { data } = req.body;

  const terminal = terminals.get(id);
  const session = terminalSessions.get(id);

  if (!terminal || !session) {
    return res.status(404).json({ error: 'Terminal not found' });
  }

  try {
    terminal.write(data);

    session.history.push({
      type: 'input',
      data,
      timestamp: new Date().toISOString()
    });

    console.log(chalk.cyan(`📝 Input sent to ${session.programName}: "${data.replace(/\r?\n/g, '\\n')}"`));

    res.json({ success: true });
  } catch (error) {
    console.error(chalk.red(`❌ Failed to send input to terminal ${id}:`), error.message);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/terminals/:id/stop', (req, res) => {
  const { id } = req.params;
  const terminal = terminals.get(id);
  const session = terminalSessions.get(id);

  if (!terminal || !session) {
    return res.status(404).json({ error: 'Terminal not found' });
  }

  try {
    terminal.kill();
    session.status = 'stopped';

    console.log(chalk.yellow(`🛑 Stopped ${session.programName}`));

    res.json({ success: true });
  } catch (error) {
    console.error(chalk.red(`❌ Failed to stop terminal ${id}:`), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(chalk.green(`🔌 Client connected: ${socket.id}`));

  // Send current active terminals to new client
  const activeTerminals = Array.from(terminalSessions.values()).map(session => ({
    id: session.id,
    programId: session.programId,
    programName: session.programName,
    status: session.status,
    startTime: session.startTime
  }));

  socket.emit('active-terminals', activeTerminals);

  // Handle terminal input from client
  socket.on('terminal-input', (data) => {
    const { terminalId, data: inputData } = data;
    const terminal = terminals.get(terminalId);
    const session = terminalSessions.get(terminalId);

    if (terminal && inputData !== undefined && inputData !== null) {
      try {
        terminal.write(inputData);

        if (session) {
          session.history.push({
            type: 'input',
            data: inputData,
            timestamp: new Date().toISOString()
          });

          console.log(chalk.cyan(`📝 Input sent to ${session.programName}: "${inputData.replace(/\r?\n/g, '\\n')}"`));
        }
      } catch (error) {
        console.error(chalk.red(`❌ Failed to write to terminal ${terminalId}:`), error.message);
      }
    } else {
      console.warn(chalk.yellow(`⚠️ Invalid input data for terminal ${terminalId}:`, inputData));
    }
  });

  // Handle terminal history requests
  socket.on('request-terminal-history', (data) => {
    const { terminalId } = data;
    const session = terminalSessions.get(terminalId);

    if (session && session.history) {
      console.log(chalk.blue(`📜 Sending terminal history for ${terminalId} (${session.history.length} entries)`));

      // Send all historical output data
      session.history.forEach(entry => {
        if (entry.type === 'output') {
          socket.emit('terminal-data', {
            terminalId,
            programId: session.programId,
            programName: session.programName,
            data: entry.data,
            timestamp: entry.timestamp
          });
        }
      });
    }
  });

  // Handle terminal resize
  socket.on('terminal-resize', (data) => {
    const { terminalId, cols, rows } = data;
    const terminal = terminals.get(terminalId);

    if (terminal) {
      terminal.resize(cols, rows);
    }
  });

  socket.on('disconnect', () => {
    console.log(chalk.gray(`🔌 Client disconnected: ${socket.id}`));
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, async () => {
  console.log(chalk.bold.blue(`
╔══════════════════════════════════════════════════════════════╗
║                 🚀 TERMINAL DASHBOARD SERVER                 ║
║                                                              ║
║  Server running on: http://localhost:${PORT}                     ║
║  WebSocket ready for real-time terminal connections         ║
║                                                              ║
║  Features:                                                   ║
║  ✅ True terminal emulation with node-pty                   ║
║  ✅ Real-time WebSocket communication                        ║
║  ✅ Multiple concurrent programs                             ║
║  ✅ Interactive input/output                                 ║
║  ✅ Session management                                       ║
║  ✅ Auto-detect programs                                     ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
  `));

  // Initialize programs after server starts
  await initializePrograms();
});

export default app;
