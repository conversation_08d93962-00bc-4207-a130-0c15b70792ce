[{"id": "0gstorage", "name": "0g Storage Auto Bot", "description": "Automated bot for interacting with the 0G Storage Network to maximize airdrop potential", "directory": "0g-Storage", "command": "npm", "args": ["start"], "type": "storage", "icon": "🌐", "color": "#3B82F6"}, {"id": "0g", "name": "0gv3 Bot", "description": "auto swap 0g-testnet", "directory": "0g", "command": "npm", "args": ["start"], "type": "application", "icon": "🌐", "color": "#84CC16"}, {"id": "huddle", "name": "Auto Deploy", "description": "Auto Deploy application", "directory": "<PERSON><PERSON>", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#22D3EE"}, {"id": "euc<PERSON>bot", "name": "<PERSON><PERSON><PERSON>", "description": "Airdrop Seeker for Euclid Testnet (ETH to EUCLID, ANDR, MON swaps)", "directory": "Euclid-Bot", "command": "npm", "args": ["start"], "type": "bot", "icon": "🤖", "color": "#8B5CF6"}, {"id": "rome", "name": "Evm <PERSON>loyer", "description": "Evm Deployer application", "directory": "rome", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#EA580C"}, {"id": "infts", "name": "Infts Auto Bot", "description": "A Node.js script for automating iNFT minting and interaction on the Sui testnet", "directory": "INFTS", "command": "npm", "args": ["start"], "type": "nft", "icon": "🎨", "color": "#F59E0B"}, {"id": "megaai", "name": "Megaai", "description": "Megaai application", "directory": "megaai", "command": "node", "args": ["index.js"], "type": "ai", "icon": "🧠", "color": "#EC4899"}, {"id": "merak", "name": "Merak", "description": "Merak application", "directory": "merak", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#F59E0B"}, {"id": "asterautobotnte", "name": "NT EXHAUST   ASTER AUTO BOT", "description": "Aster Auto Bot Daily Task", "directory": "AsterAutoBot-NTE", "command": "npm", "args": ["start"], "type": "bot", "icon": "🤖", "color": "#10B981"}, {"id": "shift", "name": "NT EXHAUST   CoinShift Auto Bot", "description": "Bot Auto Daily Checkin & Complete Task CoinShift", "directory": "Shift", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#06B6D4"}, {"id": "enso", "name": "NT EXHAUST   ENSO AUTO BOT", "description": "NT EXHAUST   ENSO AUTO BOT application", "directory": "<PERSON><PERSON>", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#EA580C"}, {"id": "inco", "name": "NT Exhaust   INCO Auto Bot", "description": "NT Exhaust   INCO Auto Bot application", "directory": "Inco", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#A855F7"}, {"id": "kite", "name": "NT EXHAUST   KITE AI AUTO BOT", "description": "NT EXHAUST   KITE AI AUTO BOT application", "directory": "kite", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#FB7185"}, {"id": "mait<PERSON>", "name": "NT Exhaust   Maitrix Auto Tool", "description": "NT Exhaust   Maitrix Auto Tool application", "directory": "<PERSON><PERSON><PERSON>", "command": "npm", "args": ["start"], "type": "ai", "icon": "🧠", "color": "#22D3EE"}, {"id": "nexyaiautobotnte", "name": "NT EXHAUST   NEXY AI AUTO BOT", "description": "NEXY AI Auto Bot Daily Task", "directory": "NexyAiAutoBot-NTE", "command": "npm", "args": ["start"], "type": "bot", "icon": "🤖", "color": "#EC4899"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "NT Exhaust   Quills Auto Send Fun", "description": "NT Exhaust   Quills Auto Send Fun automation tool", "directory": "quillau<PERSON>bot-nte", "command": "npm", "args": ["start"], "type": "bot", "icon": "🤖", "color": "#FB7185"}, {"id": "quill", "name": "NT Exhaust   Quills Auto Send Fun", "description": "NT Exhaust   Quills Auto Send Fun application", "directory": "quill", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#3B82F6"}, {"id": "r2nte", "name": "NT Exhaust   R2 Testnet Auto Bot", "description": "NT Exhaust   R2 Testnet Auto Bot application", "directory": "R2nte", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#FB7185"}, {"id": "somniaexchangebotnte", "name": "NT Exhaust   Somnia Exchange Auto Bot", "description": "NT Exhaust   Somnia Exchange Auto Bot automation tool", "directory": "SomniaExchangeBot-NTE", "command": "npm", "args": ["start"], "type": "bot", "icon": "🤖", "color": "#F472B6"}, {"id": "pharos", "name": "Pharos Auto Bot", "description": "Pharos Auto Bot application", "directory": "<PERSON><PERSON><PERSON>", "command": "npm", "args": ["start"], "type": "application", "icon": "⚡", "color": "#14B8A6"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Quillautobot Test automation tool", "directory": "quillautobot-test", "command": "node", "args": ["index.js"], "type": "bot", "icon": "🤖", "color": "#4F46E5"}, {"id": "r2", "name": "R2money Auto Bot", "description": "Automated bot for R2 Money protocol interactions on Sepolia testnet", "directory": "R2", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#F97316"}, {"id": "rise", "name": "Rise Testnet Bot", "description": "A bot for interacting with Rise Testnet, including random transfers, Gas Pump operations, and Inari Bank functions.", "directory": "Rise", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#A855F7"}, {"id": "byte", "name": "SOUIY   Bytenova Auto Bot", "description": "Bytenova Auto Daily Check-In And Task", "directory": "Byte", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#7C3AED"}, {"id": "t1", "name": "SOUIY   T1 Auto Bridge", "description": "SOUIY   T1 Auto Bridge application", "directory": "T1", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#A855F7"}, {"id": "testpyth<PERSON><PERSON>", "name": "Test Python Bot", "description": "Test Python Bot automation tool", "directory": "test-python-bot", "command": "./run-python.sh", "args": ["main.py"], "requiresVenv": true, "type": "bot", "icon": "🤖", "color": "#F472B6"}, {"id": "testscrollbot", "name": "Test <PERSON><PERSON>", "description": "Test Scroll Bot automation tool", "directory": "test-scroll-bot", "command": "./run-python.sh", "args": ["main.py"], "requiresVenv": true, "type": "bot", "icon": "🤖", "color": "#8B5CF6"}, {"id": "tradegpt", "name": "Tradegpt Auto Bot", "description": "Automated bot for interacting with TradeGPT Finance on 0G Testnet", "directory": "TradeGPT", "command": "npm", "args": ["start"], "type": "trading", "icon": "💹", "color": "#FB7185"}, {"id": "union", "name": "Union Auto Bot", "description": "A Union Testnet automation bot with Blessed terminal UI, transaction charts, and more.", "directory": "Union", "command": "npm", "args": ["start"], "type": "application", "icon": "📦", "color": "#84CC16"}, {"id": "uuidprogramc5161118", "name": "Uuid Program C5161118", "description": "Uuid Program C5161118 application", "directory": "uuid-program-c5161118", "command": "./run-python.sh", "args": ["main.py"], "requiresVenv": true, "type": "application", "icon": "📦", "color": "#FB7185"}]