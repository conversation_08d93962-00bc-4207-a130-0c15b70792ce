import React, { useState, useEffect } from 'react';
import { 
  X, 
  Save, 
  File, 
  Folder, 
  Plus, 
  Trash2, 
  Edit3,
  FolderPlus,
  AlertTriangle,
  Download
} from 'lucide-react';

function FileEditor({ isOpen, onClose, programId, programName }) {
  const [files, setFiles] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [showNewFileModal, setShowNewFileModal] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [isNewDirectory, setIsNewDirectory] = useState(false);
  const [expandedFolders, setExpandedFolders] = useState(new Set());

  useEffect(() => {
    if (isOpen && programId) {
      loadFiles();
    }
  }, [isOpen, programId]);

  const loadFiles = async () => {
    setIsLoading(true);
    setError('');
    try {
      const response = await fetch(`/api/programs/${programId}/files`);
      if (response.ok) {
        const data = await response.json();
        setFiles(data);
      } else {
        setError('Failed to load files');
      }
    } catch (err) {
      setError('Error loading files: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const loadFileContent = async (filePath) => {
    setIsLoading(true);
    setError('');
    try {
      const response = await fetch(`/api/programs/${programId}/files/${filePath}`);
      if (response.ok) {
        const data = await response.json();
        if (data.isBinary) {
          setError('Binary files cannot be edited');
          setFileContent('');
        } else {
          setFileContent(data.content);
          setSelectedFile(filePath);
        }
      } else {
        setError('Failed to load file content');
      }
    } catch (err) {
      setError('Error loading file: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const saveFile = async () => {
    if (!selectedFile) return;
    
    setIsSaving(true);
    setError('');
    try {
      const response = await fetch(`/api/programs/${programId}/files/${selectedFile}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: fileContent }),
      });

      if (response.ok) {
        console.log('File saved successfully');
      } else {
        setError('Failed to save file');
      }
    } catch (err) {
      setError('Error saving file: ' + err.message);
    } finally {
      setIsSaving(false);
    }
  };

  const createFile = async () => {
    if (!newFileName.trim()) return;

    setError('');
    try {
      const response = await fetch(`/api/programs/${programId}/files`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filePath: newFileName,
          content: '',
          isDirectory: isNewDirectory
        }),
      });

      if (response.ok) {
        setShowNewFileModal(false);
        setNewFileName('');
        setIsNewDirectory(false);
        loadFiles();
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to create file');
      }
    } catch (err) {
      setError('Error creating file: ' + err.message);
    }
  };

  const deleteFile = async (filePath) => {
    if (!confirm(`Are you sure you want to delete ${filePath}?`)) return;

    setError('');
    try {
      const response = await fetch(`/api/programs/${programId}/files/${filePath}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        if (selectedFile === filePath) {
          setSelectedFile(null);
          setFileContent('');
        }
        loadFiles();
      } else {
        setError('Failed to delete file');
      }
    } catch (err) {
      setError('Error deleting file: ' + err.message);
    }
  };

  const toggleFolder = (folderPath) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderPath)) {
      newExpanded.delete(folderPath);
    } else {
      newExpanded.add(folderPath);
    }
    setExpandedFolders(newExpanded);
  };

  const renderFileTree = (items, level = 0) => {
    return items.map((item) => (
      <div key={item.path} style={{ marginLeft: `${level * 20}px` }}>
        <div className="flex items-center justify-between py-1 px-2 hover:bg-terminal-border rounded group">
          <div 
            className="flex items-center gap-2 cursor-pointer flex-1"
            onClick={() => {
              if (item.type === 'directory') {
                toggleFolder(item.path);
              } else {
                loadFileContent(item.path);
              }
            }}
          >
            {item.type === 'directory' ? (
              <Folder className="w-4 h-4 text-blue-400" />
            ) : (
              <File className="w-4 h-4 text-terminal-muted" />
            )}
            <span className="text-sm text-terminal-text">{item.name}</span>
          </div>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              deleteFile(item.path);
            }}
            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-500/20 rounded"
            title="Delete"
          >
            <Trash2 className="w-3 h-3 text-red-400" />
          </button>
        </div>
        
        {item.type === 'directory' && expandedFolders.has(item.path) && item.children && (
          <div>
            {renderFileTree(item.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-terminal-surface border border-terminal-border rounded-lg w-full max-w-6xl h-5/6 mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-terminal-border">
          <div className="flex items-center gap-3">
            <Edit3 className="text-blue-400" size={24} />
            <h2 className="text-xl font-semibold text-terminal-text">
              File Editor - {programName}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-terminal-muted hover:text-terminal-text"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="flex flex-1 overflow-hidden">
          {/* File Tree */}
          <div className="w-1/3 border-r border-terminal-border p-4 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-terminal-text">Files</h3>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    setIsNewDirectory(false);
                    setShowNewFileModal(true);
                  }}
                  className="p-1 hover:bg-terminal-border rounded"
                  title="New File"
                >
                  <Plus className="w-4 h-4 text-green-400" />
                </button>
                <button
                  onClick={() => {
                    setIsNewDirectory(true);
                    setShowNewFileModal(true);
                  }}
                  className="p-1 hover:bg-terminal-border rounded"
                  title="New Folder"
                >
                  <FolderPlus className="w-4 h-4 text-blue-400" />
                </button>
              </div>
            </div>

            {isLoading ? (
              <div className="text-terminal-muted">Loading files...</div>
            ) : (
              <div className="space-y-1">
                {renderFileTree(files)}
              </div>
            )}
          </div>

          {/* Editor */}
          <div className="flex-1 flex flex-col">
            {selectedFile ? (
              <>
                <div className="flex items-center justify-between p-4 border-b border-terminal-border">
                  <span className="text-terminal-text font-medium">{selectedFile}</span>
                  <button
                    onClick={saveFile}
                    disabled={isSaving}
                    className="btn-primary flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    {isSaving ? 'Saving...' : 'Save'}
                  </button>
                </div>
                
                <textarea
                  value={fileContent}
                  onChange={(e) => setFileContent(e.target.value)}
                  className="flex-1 p-4 bg-terminal-bg text-terminal-text font-mono text-sm resize-none border-none outline-none"
                  placeholder="File content..."
                />
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center text-terminal-muted">
                <div className="text-center">
                  <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Select a file to edit</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-4 border-t border-terminal-border">
            <div className="flex items-center gap-2 text-red-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">{error}</span>
            </div>
          </div>
        )}
      </div>

      {/* New File Modal */}
      {showNewFileModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-terminal-surface border border-terminal-border rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-terminal-text mb-4">
              Create New {isNewDirectory ? 'Folder' : 'File'}
            </h3>
            
            <input
              type="text"
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              placeholder={`${isNewDirectory ? 'Folder' : 'File'} name...`}
              className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded text-terminal-text mb-4"
              onKeyPress={(e) => e.key === 'Enter' && createFile()}
            />
            
            <div className="flex gap-3">
              <button
                onClick={() => setShowNewFileModal(false)}
                className="btn-secondary flex-1"
              >
                Cancel
              </button>
              <button
                onClick={createFile}
                className="btn-primary flex-1"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default FileEditor;
