import React from 'react';
import { X, AlertTriangle, Shield, Eye, Download } from 'lucide-react';

function EncryptionWarningModal({ 
  isOpen, 
  onClose, 
  onContinue, 
  onCancel, 
  encryptedFiles = [],
  programName,
  githubUrl 
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-terminal-surface border border-terminal-border rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-terminal-border">
          <div className="flex items-center gap-3">
            <AlertTriangle className="text-red-400" size={24} />
            <h2 className="text-xl font-semibold text-terminal-text">
              Encryption/Obfuscation Detected
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-terminal-muted hover:text-terminal-text"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <Shield className="text-yellow-400" size={20} />
              <h3 className="text-lg font-semibold text-terminal-text">Security Warning</h3>
            </div>
            
            <p className="text-terminal-muted mb-4">
              The program <strong className="text-terminal-text">{programName}</strong> from{' '}
              <span className="text-blue-400">{githubUrl}</span> contains potentially 
              encrypted or obfuscated code that could be malicious.
            </p>

            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
              <h4 className="text-red-400 font-semibold mb-2">⚠️ Potential Risks:</h4>
              <ul className="text-sm text-terminal-muted space-y-1">
                <li>• Hidden malicious code that could steal your data</li>
                <li>• Cryptocurrency wallet theft or unauthorized transactions</li>
                <li>• System compromise or backdoor installation</li>
                <li>• Data exfiltration or privacy violations</li>
                <li>• Botnet participation or illegal activities</li>
              </ul>
            </div>
          </div>

          {/* Detected Files */}
          <div className="mb-6">
            <h4 className="text-terminal-text font-semibold mb-3 flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Suspicious Files Detected ({encryptedFiles.length})
            </h4>
            
            <div className="bg-terminal-bg border border-terminal-border rounded-lg max-h-48 overflow-y-auto">
              {encryptedFiles.map((file, index) => (
                <div key={index} className="p-3 border-b border-terminal-border last:border-b-0">
                  <div className="flex items-center justify-between">
                    <span className="text-terminal-text font-mono text-sm">{file.file}</span>
                    <div className="flex gap-2 text-xs">
                      <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded">
                        Suspicious: {file.suspiciousCount}
                      </span>
                      <span className="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">
                        Entropy: {file.entropy}
                      </span>
                    </div>
                  </div>
                  
                  {file.patterns && file.patterns.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-terminal-muted mb-1">Detected patterns:</p>
                      <div className="flex flex-wrap gap-1">
                        {file.patterns.slice(0, 3).map((pattern, i) => (
                          <span key={i} className="text-xs bg-terminal-border text-terminal-muted px-2 py-1 rounded font-mono">
                            {pattern.replace(/[\/\\]/g, '').substring(0, 20)}...
                          </span>
                        ))}
                        {file.patterns.length > 3 && (
                          <span className="text-xs text-terminal-muted">
                            +{file.patterns.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recommendations */}
          <div className="mb-6">
            <h4 className="text-terminal-text font-semibold mb-3">🛡️ Security Recommendations:</h4>
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <ul className="text-sm text-terminal-muted space-y-2">
                <li>• <strong>Review the source code</strong> manually before running</li>
                <li>• <strong>Run in isolated environment</strong> (VM or container)</li>
                <li>• <strong>Check repository reputation</strong> and community feedback</li>
                <li>• <strong>Scan with antivirus</strong> and security tools</li>
                <li>• <strong>Monitor network activity</strong> when running</li>
                <li>• <strong>Use test accounts</strong> with minimal funds</li>
              </ul>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={onCancel}
              className="btn-secondary flex-1 flex items-center justify-center gap-2"
            >
              <X className="w-4 h-4" />
              Cancel & Delete
            </button>
            
            <button
              onClick={onContinue}
              className="btn-danger flex-1 flex items-center justify-center gap-2"
            >
              <Download className="w-4 h-4" />
              Continue Anyway
            </button>
          </div>

          <p className="text-xs text-terminal-muted text-center mt-3">
            By continuing, you acknowledge the risks and proceed at your own responsibility.
          </p>
        </div>
      </div>
    </div>
  );
}

export default EncryptionWarningModal;
