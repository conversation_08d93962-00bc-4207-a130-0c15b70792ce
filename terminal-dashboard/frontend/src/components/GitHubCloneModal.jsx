import React, { useState } from 'react';
import { X, Github, Download, AlertCircle, CheckCircle, Loader } from 'lucide-react';

function GitHubCloneModal({ isOpen, onClose, onClone }) {
  const [repoUrl, setRepoUrl] = useState('');
  const [customName, setCustomName] = useState('');
  const [isCloning, setIsCloning] = useState(false);
  const [cloneStatus, setCloneStatus] = useState(null);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!repoUrl.trim()) {
      setError('Repository URL is required');
      return;
    }

    // Validate GitHub URL
    const githubUrlPattern = /^https:\/\/github\.com\/[\w\-\.]+\/[\w\-\.]+(?:\.git)?$/;
    if (!githubUrlPattern.test(repoUrl.trim())) {
      setError('Please enter a valid GitHub repository URL');
      return;
    }

    setIsCloning(true);
    setError('');
    setCloneStatus('cloning');

    try {
      // Extract repository name from URL if no custom name provided
      let programName = customName.trim();
      if (!programName) {
        const urlParts = repoUrl.trim().split('/');
        programName = urlParts[urlParts.length - 1].replace('.git', '');
      }

      const response = await fetch('/api/programs/clone', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          githubUrl: repoUrl.trim(),
          programName: programName
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setCloneStatus('success');
        setTimeout(() => {
          onClone(result);
          handleClose();
        }, 2000);
      } else {
        setError(result.error || 'Failed to clone repository');
        setCloneStatus('error');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
      setCloneStatus('error');
    } finally {
      setIsCloning(false);
    }
  };

  const handleClose = () => {
    setRepoUrl('');
    setCustomName('');
    setIsCloning(false);
    setCloneStatus(null);
    setError('');
    onClose();
  };

  const getStatusIcon = () => {
    switch (cloneStatus) {
      case 'cloning':
        return <Loader className="animate-spin" size={20} />;
      case 'success':
        return <CheckCircle className="text-green-500" size={20} />;
      case 'error':
        return <AlertCircle className="text-red-500" size={20} />;
      default:
        return <Github size={20} />;
    }
  };

  const getStatusMessage = () => {
    switch (cloneStatus) {
      case 'cloning':
        return 'Cloning repository and setting up dependencies...';
      case 'success':
        return 'Repository cloned successfully! Adding to dashboard...';
      case 'error':
        return error;
      default:
        return '';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-terminal-surface border border-terminal-border rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-terminal-border">
          <div className="flex items-center space-x-2">
            <Github className="text-blue-400" size={24} />
            <h2 className="text-xl font-semibold text-terminal-text">Clone from GitHub</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isCloning}
            className="text-terminal-muted hover:text-terminal-text disabled:opacity-50"
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* Repository URL */}
            <div>
              <label htmlFor="repoUrl" className="block text-sm font-medium text-terminal-text mb-2">
                GitHub Repository URL *
              </label>
              <input
                type="url"
                id="repoUrl"
                value={repoUrl}
                onChange={(e) => setRepoUrl(e.target.value)}
                placeholder="https://github.com/username/repository"
                className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-terminal-text"
                disabled={isCloning}
                required
              />
            </div>

            {/* Custom Name */}
            <div>
              <label htmlFor="customName" className="block text-sm font-medium text-terminal-text mb-2">
                Custom Directory Name (optional)
              </label>
              <input
                type="text"
                id="customName"
                value={customName}
                onChange={(e) => setCustomName(e.target.value)}
                placeholder="Leave empty to use repository name"
                className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-terminal-text"
                disabled={isCloning}
              />
            </div>

            {/* Status Message */}
            {(cloneStatus || error) && (
              <div className={`flex items-center space-x-2 p-3 rounded-md ${
                cloneStatus === 'success' ? 'bg-green-50 text-green-700' :
                cloneStatus === 'error' ? 'bg-red-50 text-red-700' :
                'bg-blue-50 text-blue-700'
              }`}>
                {getStatusIcon()}
                <span className="text-sm">{getStatusMessage()}</span>
              </div>
            )}

            {/* Info */}
            <div className="bg-gray-50 p-3 rounded-md">
              <h4 className="text-sm font-medium text-gray-700 mb-2">What will happen:</h4>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• Clone the repository to your workspace</li>
                <li>• Auto-detect project type (Node.js, Python, Go, etc.)</li>
                <li>• Install required dependencies automatically</li>
                <li>• Generate Terminal Dashboard configuration</li>
                <li>• Add program to your dashboard</li>
              </ul>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              disabled={isCloning}
              className="btn-secondary flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCloning || !repoUrl.trim()}
              className="btn-primary flex-1 flex items-center justify-center space-x-2"
            >
              {isCloning ? (
                <>
                  <Loader className="animate-spin" size={16} />
                  <span>Cloning...</span>
                </>
              ) : (
                <>
                  <Download size={16} />
                  <span>Clone Repository</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default GitHubCloneModal;
