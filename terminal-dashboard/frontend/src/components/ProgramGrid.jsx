import React, { useState } from 'react';
import { Play, Square, Clock, Zap, Trash2, AlertTriangle } from 'lucide-react';

function ProgramGrid({ programs, terminals, onStartProgram, onStopProgram, onViewTerminal, onDeleteProgram }) {
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  const getTerminalForProgram = (programId) => {
    return terminals.find(t => t.programId === programId && t.status === 'running');
  };

  const handleDeleteProgram = async (programId) => {
    if (onDeleteProgram) {
      await onDeleteProgram(programId);
    }
    setDeleteConfirm(null);
  };

  const formatUptime = (startTime) => {
    const start = new Date(startTime);
    const now = new Date();
    const diff = Math.floor((now - start) / 1000);

    if (diff < 60) return `${diff}s`;
    if (diff < 3600) return `${Math.floor(diff / 60)}m`;
    return `${Math.floor(diff / 3600)}h ${Math.floor((diff % 3600) / 60)}m`;
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Available Programs</h2>
        <div className="text-sm text-terminal-muted">
          {programs.length} programs configured
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {programs.map((program) => {
          const terminal = getTerminalForProgram(program.id);
          const isRunning = !!terminal;

          return (
            <div
              key={program.id}
              className={`program-card ${isRunning ? 'running' : ''}`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div
                    className="p-3 rounded-lg text-2xl"
                    style={{
                      backgroundColor: `${program.color}20`,
                      color: program.color
                    }}
                  >
                    {program.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{program.name}</h3>
                    <p className="text-sm text-terminal-muted capitalize">
                      {program.type}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {isRunning ? (
                    <div className="status-dot status-running"></div>
                  ) : (
                    <div className="status-dot status-stopped"></div>
                  )}
                </div>
              </div>

              <p className="text-terminal-muted text-sm mb-4 leading-relaxed">
                {program.description}
              </p>

              {isRunning && (
                <div className="bg-terminal-bg rounded-lg p-3 mb-4 border border-green-500/20">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-green-400" />
                      <span className="text-terminal-muted">Uptime:</span>
                      <span className="text-green-400 font-medium">
                        {formatUptime(terminal.startTime)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Zap className="w-4 h-4 text-blue-400" />
                      <span className="text-blue-400 font-medium">
                        PID: {terminal.pid}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2">
                {isRunning ? (
                  <>
                    <button
                      onClick={() => onStopProgram && onStopProgram(terminal.id)}
                      className="btn-danger flex-1 flex items-center justify-center gap-2"
                      title="Stop Program"
                    >
                      <Square className="w-4 h-4" />
                      Stop
                    </button>
                    <button
                      className="btn-primary px-4"
                      onClick={() => onViewTerminal && onViewTerminal(terminal.id)}
                      title="View Terminal"
                    >
                      View
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => onStartProgram(program.id)}
                      className="btn-primary flex-1 flex items-center justify-center gap-2"
                      title="Start Program"
                    >
                      <Play className="w-4 h-4" />
                      Start
                    </button>
                    <button
                      onClick={() => setDeleteConfirm(program.id)}
                      className="btn-danger px-3 flex items-center justify-center"
                      title="Delete Program"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </>
                )}
              </div>

              <div className="mt-3 pt-3 border-t border-terminal-border">
                <div className="flex items-center justify-between text-xs text-terminal-muted">
                  <span>Command: {program.command} {program.args.join(' ')}</span>
                  <span>Dir: {program.directory}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {programs.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🤖</div>
          <h3 className="text-xl font-semibold mb-2">No Programs Configured</h3>
          <p className="text-terminal-muted">
            Add your testnet programs to the configuration to get started.
          </p>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-terminal-surface border border-terminal-border rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-400" />
              <h3 className="text-lg font-semibold">Delete Program</h3>
            </div>
            <p className="text-terminal-muted mb-6">
              Are you sure you want to delete this program? This will permanently remove the program directory and all its files. This action cannot be undone.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="btn-secondary flex-1"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteProgram(deleteConfirm)}
                className="btn-danger flex-1"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ProgramGrid;
