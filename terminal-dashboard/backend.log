
> terminal-dashboard-backend@1.0.0 start
> node server.js


╔══════════════════════════════════════════════════════════════╗
║                 🚀 TERMINAL DASHBOARD SERVER                 ║
║                                                              ║
║  Server running on: http://localhost:3001                     ║
║  WebSocket ready for real-time terminal connections         ║
║                                                              ║
║  Features:                                                   ║
║  ✅ True terminal emulation with node-pty                   ║
║  ✅ Real-time WebSocket communication                        ║
║  ✅ Multiple concurrent programs                             ║
║  ✅ Interactive input/output                                 ║
║  ✅ Session management                                       ║
║  ✅ Auto-detect programs                                     ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
  
🔍 Initializing programs...
📂 Loaded 29 programs from config
🔍 Auto-detecting programs...
📦 Detected: 0gv3 Bot (application)
📦 Detected: 0g Storage Auto Bot (storage)
📦 Detected: NT EXHAUST   ASTER AUTO BOT (bot)
📦 Detected: SOUIY   Bytenova Auto Bot (application)
📦 Detected: NT EXHAUST   ENSO AUTO BOT (application)
📦 Detected: Euclid Bot (bot)
📦 Detected: Auto Deploy (application)
📦 Detected: Infts Auto Bot (nft)
📦 Detected: NT Exhaust   INCO Auto Bot (application)
📦 Detected: NT Exhaust   Maitrix Auto Tool (ai)
📦 Detected: NT EXHAUST   NEXY AI AUTO BOT (bot)
📦 Detected: Pharos Auto Bot (application)
📦 Detected: R2money Auto Bot (application)
📦 Detected: NT Exhaust   R2 Testnet Auto Bot (application)
📦 Detected: Rise Testnet Bot (application)
📦 Detected: NT EXHAUST   CoinShift Auto Bot (application)
📦 Detected: NT Exhaust   Somnia Exchange Auto Bot (bot)
📦 Detected: SOUIY   T1 Auto Bridge (application)
📦 Detected: Tradegpt Auto Bot (trading)
📦 Detected: Union Auto Bot (application)
📦 Detected: NT EXHAUST   KITE AI AUTO BOT (application)
📦 Detected: Megaai (ai)
📦 Detected: Merak (application)
📦 Detected: NT Exhaust   Quills Auto Send Fun (application)
📦 Detected: NT Exhaust   Quills Auto Send Fun (bot)
📦 Detected: Evm Deployer (application)
✅ Detected 26 programs
💾 Saved 29 programs to config
✅ Loaded 29 programs
