"use strict";

module.exports = function (grunt) {
  // Project configuration.
  grunt.initConfig({
    jshint: {
      all: ["Gruntfile.js", "lib/*.js", "<%= nodeunit.tests %>"],
      options: {
        jshintrc: ".jshintrc",
      },
    },

    // Before generating any new files, remove any previously-created files.
    clean: {
      tests: ["tmp"],
    },

    // Unit tests.
    nodeunit: {
      tests: ["test/*_test.js"],
    },
  });

  // These plugins provide necessary tasks.
  grunt.loadNpmTasks("grunt-contrib-jshint");
  grunt.loadNpmTasks("grunt-contrib-clean");
  grunt.loadNpmTasks("grunt-contrib-nodeunit");

  // Whenever the "test" task is run, first clean the "tmp" dir, then run this
  // plugin's task(s), then test the result.
  grunt.registerTask("test", ["clean", "nodeunit"]);

  // By default, lint and run all tests.
  grunt.registerTask("default", ["jshint", "test"]);
};
