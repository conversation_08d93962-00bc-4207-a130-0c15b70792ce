export default `flf2a$ 8 7 15 0 12 0 16255 0
Author : <PERSON><PERSON>
Date   : 2004/6/26 13:47:42
Version: 1.0
Title  : twisted
-------------------------------------------------

-------------------------------------------------
This font has been created using <PERSON><PERSON><PERSON>'s FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.
$ #
$ #
$ #
$ #
$ #
$ #
$ #
$ ##
  _    #
 /_/\\  #
 ) ) ) #
/_/ /  #
\\_\\_\\  #
 /_/\\  #
 )_)/  #
       ##
"#
 #
 #
 #
 #
 #
 #
 ##
##
 #
 #
 #
 #
 #
 #
 ##
$#
 #
 #
 #
 #
 #
 #
 ##
%#
 #
 #
 #
 #
 #
 #
 ##
&#
 #
 #
 #
 #
 #
 #
 ##
 _   #
/_/\\ #
\\_\\/ #
     #
     #
     #
     #
     ##
  __   #
 /\\_\\  #
( ( (  #
 \\ \\_\\ #
 / / / #
( (_(  #
 \\/_/  #
       ##
  __   #
 /_/\\  #
 ) ) ) #
/_/ /  #
\\ \\ \\  #
 )_) ) #
 \\_\\/  #
       ##
*#
 #
 #
 #
 #
 #
 #
 ##
+#
 #
 #
 #
 #
 #
 #
 ##
     #
     #
     #
     #
 _   #
/_/\\ #
)_)/ #
     ##
-#
 #
 #
 #
 #
 #
 #
 ##
     #
     #
     #
     #
 _   #
/_/\\ #
\\_\\/ #
     ##
   __   #
  /_/\\  #
  ) ) ) #
 /_/ /  #
 ) ) )  #
/ / /   #
\\/_/    #
        ##
   _____    #
  / ___ \\   #
 / /\\_/\\ \\  #
/ /_/ (_\\ \\ #
\\ \\ )_/ / / #
 \\ \\/_\\/ /  #
  \\ ____/   #
            ##
 _ __     #
/_ \\ \\    #
  ) ) )   #
 / / /    #
 \\ \\ \\_   #
  ) )__/\\ #
  \\/___\\/ #
          ##
  _ __     #
 /_ \\ \\    #
(/_) ) )   #
  / / /    #
  \\ \\ \\_   #
   ) )__/\\ #
   \\/___\\/ #
           ##
  _____    #
 /\\__  /\\  #
(_( _)( (  #
    __ \\ \\ #
 __ _  / / #
( (__)( (  #
 \\/____\\/  #
           ##
   ____    #
  /\\__/\\   #
 / /_ \\ \\  #
/ /(_)/ /  #
\\/___ \\ \\  #
     )_) ) #
     \\_\\/  #
           ##
 __ ____   #
/ /  __/\\  #
) ) ( _\\/  #
\\_\\____ (  #
   __\\ \\ \\ #
 /\\__/ / / #
 \\/____\\/  #
           ##
     _    #
    /_/\\  #
    )_) ) #
 _ _// /  #
/\\(_)\\ \\  #
\\ \\__/ /  #
 \\/__\\/   #
          ##
 ___ __   #
/___ \\ \\  #
    ) ) ) #
   / / /  #
   \\ \\ \\  #
    ) ) ) #
    \\/_/  #
          ##
  ____    #
 /\\___/\\  #
/ / _ \\ \\ #
\\ \\( )/ / #
/ /(_)\\ \\ #
\\ \\___/ / #
 \\/___\\/  #
          ##
  _____    #
 /\\___/\\   #
/ / _ \\ \\  #
\\ \\( )/ /  #
 \\/_/ \\ \\  #
     )_) ) #
     \\_\\/  #
           ##
 _   #
/_/\\ #
\\_\\/ #
     #
 _   #
/_/\\ #
\\_\\/ #
     ##
 _   #
/_/\\ #
\\_\\/ #
     #
 _   #
/_/\\ #
)_)/ #
     ##
  __  #
 /\\_\\ #
/ / / #
\\ \\_\\ #
 \\/_/ #
      #
      #
      ##
=#
 #
 #
 #
 #
 #
 #
 ##
 __   #
/_/\\  #
\\ \\ \\ #
/_/ / #
\\_\\/  #
      #
      #
      ##
 _ ___    #
/_/  _/\\  #
)_) ) ) ) #
\\_\\/_/ /  #
   \\_\\_\\  #
    /_/\\  #
    \\_\\/  #
          ##
@#
 #
 #
 #
 #
 #
 #
 ##
   _____    #
  /\\___/\\   #
 / / _ \\ \\  #
 \\ \\(_)/ /  #
 / / _ \\ \\  #
( (_( )_) ) #
 \\/_/ \\_\\/  #
            ##
   _____   #
 /\\  __/\\  #
 ) )(_ ) ) #
/ / __/ /  #
\\ \\  _\\ \\  #
 ) )(__) ) #
 \\/____\\/  #
           ##
  _____  #
 /\\ __/\\ #
 ) )__\\/ #
/ / /    #
\\ \\ \\_   #
 ) )__/\\ #
 \\/___\\/ #
         ##
  _____    #
 /\\ __/\\   #
 ) )  \\ \\  #
/ / /\\ \\ \\ #
\\ \\ \\/ / / #
 ) )__/ /  #
 \\/___\\/   #
           ##
   _____  #
 /\\_____\\ #
( (_____/ #
 \\ \\__\\   #
 / /__/_  #
( (_____\\ #
 \\/_____/ #
          ##
   _____  #
 /\\_____\\ #
( (  ___/ #
 \\ \\ \\_   #
 / / /_\\  #
/ /____/  #
\\/_/      #
          ##
  ______    #
 /_/\\___\\   #
 ) ) ___/   #
/_/ /  ___  #
\\ \\ \\_/\\__\\ #
 )_)  \\/ _/ #
 \\_\\____/   #
            ##
  __   __   #
 /\\_\\ /_/\\  #
( ( (_) ) ) #
 \\ \\___/ /  #
 / / _ \\ \\  #
( (_( )_) ) #
 \\/_/ \\_\\/  #
            ##
  __   #
 /\\_\\  #
 \\/_/  #
  /\\_\\ #
 / / / #
( (_(  #
 \\/_/  #
       ##
     __   #
    /_/\\  #
    ) ) ) #
 _ /_/ /  #
/_/\\ \\ \\  #
)_) ) ) ) #
\\_\\___\\/  #
          ##
  __  __   #
 /\\_\\\\  /\\ #
( ( (/ / / #
 \\ \\_ / /  #
 / /  \\ \\  #
( (_(\\ \\ \\ #
 \\/_//__\\/ #
           ##
  __      #
 /\\_\\     #
( ( (     #
 \\ \\_\\    #
 / / /__  #
( (_____( #
 \\/_____/ #
          ##
  __    __   #
 /_/\\  /\\_\\  #
 ) ) \\/ ( (  #
/_/ \\  / \\_\\ #
\\ \\ \\\\// / / #
 )_) )( (_(  #
 \\_\\/  \\/_/  #
             ##
  __   __   #
 /_/\\ /\\_\\  #
 ) ) \\ ( (  #
/_/   \\ \\_\\ #
\\ \\ \\   / / #
 )_) \\ (_(  #
 \\_\\/ \\/_/  #
            ##
   _____    #
  ) ___ (   #
 / /\\_/\\ \\  #
/ /_/ (_\\ \\ #
\\ \\ )_/ / / #
 \\ \\/_\\/ /  #
  )_____(   #
            ##
  __ __    #
 /_/\\__/\\  #
 ) ) ) ) ) #
/_/ /_/ /  #
\\ \\ \\_\\/   #
 )_) )     #
 \\_\\/      #
           ##
   _____     #
  / ___ (    #
 / /\\_/\\ \\   #
/ /_/ (_\\ \\  #
\\ \\ )_/ / (  #
 \\ \\/_\\/ \\_\\ #
  \\_____\\/_/ #
             ##
  __ __    #
 /_/\\__/\\  #
 ) ) ) ) ) #
/_/ /_/_/  #
\\ \\ \\ \\ \\  #
 )_) ) \\ \\ #
 \\_\\/ \\_\\/ #
           ##
 ______  #
/ ____/\\ #
) ) __\\/ #
 \\ \\ \\   #
 _\\ \\ \\  #
)____) ) #
\\____\\/  #
         ##
  _______   #
/\\_______)\\ #
\\(___  __\\/ #
  / / /     #
 ( ( (      #
  \\ \\ \\     #
  /_/_/     #
            ##
  __    __   #
 /\\_\\  /_/\\  #
( ( (  ) ) ) #
 \\ \\ \\/ / /  #
  \\ \\  / /   #
  ( (__) )   #
   \\/__\\/    #
             ##
  _     _   #
 /_/\\ /\\_\\  #
 ) ) ) ( (  #
/_/ / \\ \\_\\ #
\\ \\ \\_/ / / #
 \\ \\   / /  #
  \\_\\_/_/   #
            ##
  _      _   #
 /_/\\  /\\_\\  #
 ) ) )( ( (  #
/_/ //\\\\ \\_\\ #
\\ \\ /  \\ / / #
 )_) /\\ (_(  #
 \\_\\/  \\/_/  #
             ##
  __  __   #
/\\  /\\  /\\ #
\\ \\ \\/ / / #
 \\ \\  / /  #
 / /  \\ \\  #
/ / /\\ \\ \\ #
\\/__\\/__\\/ #
           ##
  __  __   #
/\\  /\\  /\\ #
\\ \\ \\/ / / #
 \\ \\__/ /  #
  \\__/ /   #
  / / /    #
  \\/_/     #
           ##
 _____     #
/\\____\\    #
\\/_ ( (    #
   \\ \\_\\   #
   / / /__ #
  ( (____( #
   \\/____/ #
           ##
   __   #
  /\\_\\  #
 / ( (  #
/   \\_\\ #
\\   / / #
 \\ (_(  #
  \\/_/  #
        ##
  __    #
 /\\_\\   #
( ( (   #
 \\ \\_\\  #
 ( ( (  #
  \\ \\ \\ #
   \\_\\/ #
        ##
  __    #
 /_/\\   #
 ) ) \\  #
/_/   \\ #
\\ \\   / #
 )_) /  #
 \\_\\/   #
        ##
  _ __   #
 /_ \\ \\  #
(/ \\_)_) #
         #
         #
         #
         #
         ##
_#
 #
 #
 #
 #
 #
 #
 ##
 _   #
/_/\\ #
)_)/ #
     #
     #
     #
     #
     ##
   _____    #
  /\\___/\\   #
 / / _ \\ \\  #
 \\ \\(_)/ /  #
 / / _ \\ \\  #
( (_( )_) ) #
 \\/_/ \\_\\/  #
            ##
   _____   #
 /\\  __/\\  #
 ) )(_ ) ) #
/ / __/ /  #
\\ \\  _\\ \\  #
 ) )(__) ) #
 \\/____\\/  #
           ##
  _____  #
 /\\ __/\\ #
 ) )__\\/ #
/ / /    #
\\ \\ \\_   #
 ) )__/\\ #
 \\/___\\/ #
         ##
  _____    #
 /\\ __/\\   #
 ) )  \\ \\  #
/ / /\\ \\ \\ #
\\ \\ \\/ / / #
 ) )__/ /  #
 \\/___\\/   #
           ##
   _____  #
 /\\_____\\ #
( (_____/ #
 \\ \\__\\   #
 / /__/_  #
( (_____\\ #
 \\/_____/ #
          ##
   _____  #
 /\\_____\\ #
( (  ___/ #
 \\ \\ \\_   #
 / / /_\\  #
/ /____/  #
\\/_/      #
          ##
  ______    #
 /_/\\___\\   #
 ) ) ___/   #
/_/ /  ___  #
\\ \\ \\_/\\__\\ #
 )_)  \\/ _/ #
 \\_\\____/   #
            ##
  __   __   #
 /\\_\\ /_/\\  #
( ( (_) ) ) #
 \\ \\___/ /  #
 / / _ \\ \\  #
( (_( )_) ) #
 \\/_/ \\_\\/  #
            ##
  __   #
 /\\_\\  #
 \\/_/  #
  /\\_\\ #
 / / / #
( (_(  #
 \\/_/  #
       ##
     __   #
    /_/\\  #
    ) ) ) #
 _ /_/ /  #
/_/\\ \\ \\  #
)_) ) ) ) #
\\_\\___\\/  #
          ##
  __  __   #
 /\\_\\\\  /\\ #
( ( (/ / / #
 \\ \\_ / /  #
 / /  \\ \\  #
( (_(\\ \\ \\ #
 \\/_//__\\/ #
           ##
  __      #
 /\\_\\     #
( ( (     #
 \\ \\_\\    #
 / / /__  #
( (_____( #
 \\/_____/ #
          ##
  __    __   #
 /_/\\  /\\_\\  #
 ) ) \\/ ( (  #
/_/ \\  / \\_\\ #
\\ \\ \\\\// / / #
 )_) )( (_(  #
 \\_\\/  \\/_/  #
             ##
  __   __   #
 /_/\\ /\\_\\  #
 ) ) \\ ( (  #
/_/   \\ \\_\\ #
\\ \\ \\   / / #
 )_) \\ (_(  #
 \\_\\/ \\/_/  #
            ##
   _____    #
  ) ___ (   #
 / /\\_/\\ \\  #
/ /_/ (_\\ \\ #
\\ \\ )_/ / / #
 \\ \\/_\\/ /  #
  )_____(   #
            ##
  __ __    #
 /_/\\__/\\  #
 ) ) ) ) ) #
/_/ /_/ /  #
\\ \\ \\_\\/   #
 )_) )     #
 \\_\\/      #
           ##
   _____     #
  / ___ (    #
 / /\\_/\\ \\   #
/ /_/ (_\\ \\  #
\\ \\ )_/ / (  #
 \\ \\/_\\/ \\_\\ #
  \\_____\\/_/ #
             ##
  __ __    #
 /_/\\__/\\  #
 ) ) ) ) ) #
/_/ /_/_/  #
\\ \\ \\ \\ \\  #
 )_) ) \\ \\ #
 \\_\\/ \\_\\/ #
           ##
 ______  #
/ ____/\\ #
) ) __\\/ #
 \\ \\ \\   #
 _\\ \\ \\  #
)____) ) #
\\____\\/  #
         ##
  _______   #
/\\_______)\\ #
\\(___  __\\/ #
  / / /     #
 ( ( (      #
  \\ \\ \\     #
  /_/_/     #
            ##
  __    __   #
 /\\_\\  /_/\\  #
( ( (  ) ) ) #
 \\ \\ \\/ / /  #
  \\ \\  / /   #
  ( (__) )   #
   \\/__\\/    #
             ##
  _     _   #
 /_/\\ /\\_\\  #
 ) ) ) ( (  #
/_/ / \\ \\_\\ #
\\ \\ \\_/ / / #
 \\ \\   / /  #
  \\_\\_/_/   #
            ##
  _      _   #
 /_/\\  /\\_\\  #
 ) ) )( ( (  #
/_/ //\\\\ \\_\\ #
\\ \\ /  \\ / / #
 )_) /\\ (_(  #
 \\_\\/  \\/_/  #
             ##
  __  __   #
/\\  /\\  /\\ #
\\ \\ \\/ / / #
 \\ \\  / /  #
 / /  \\ \\  #
/ / /\\ \\ \\ #
\\/__\\/__\\/ #
           ##
  __  __   #
/\\  /\\  /\\ #
\\ \\ \\/ / / #
 \\ \\__/ /  #
  \\__/ /   #
  / / /    #
  \\/_/     #
           ##
 _____     #
/\\____\\    #
\\/_ ( (    #
   \\ \\_\\   #
   / / /__ #
  ( (____( #
   \\/____/ #
           ##
  __   #
 /\\_\\  #
( ( (  #
 \\ \\_\\ #
 / / / #
( (_(  #
 \\/_/  #
       ##
|#
 #
 #
 #
 #
 #
 #
 ##
  __   #
 /_/\\  #
 ) ) ) #
/_/ /  #
\\ \\ \\  #
 )_) ) #
 \\_\\/  #
       ##
~#
 #
 #
 #
 #
 #
 #
 ##
   _____    #
  /\\___/\\   #
 / / _ \\ \\  #
 \\ \\(_)/ /  #
 / / _ \\ \\  #
( (_( )_) ) #
 \\/_/ \\_\\/  #
            ##
   _____    #
  ) ___ (   #
 / /\\_/\\ \\  #
/ /_/ (_\\ \\ #
\\ \\ )_/ / / #
 \\ \\/_\\/ /  #
  )_____(   #
            ##
  __    __   #
 /\\_\\  /_/\\  #
( ( (  ) ) ) #
 \\ \\ \\/ / /  #
  \\ \\  / /   #
  ( (__) )   #
   \\/__\\/    #
             ##
   _____    #
  /\\___/\\   #
 / / _ \\ \\  #
 \\ \\(_)/ /  #
 / / _ \\ \\  #
( (_( )_) ) #
 \\/_/ \\_\\/  #
            ##
   _____    #
  ) ___ (   #
 / /\\_/\\ \\  #
/ /_/ (_\\ \\ #
\\ \\ )_/ / / #
 \\ \\/_\\/ /  #
  )_____(   #
            ##
  __    __   #
 /\\_\\  /_/\\  #
( ( (  ) ) ) #
 \\ \\ \\/ / /  #
  \\ \\  / /   #
  ( (__) )   #
   \\/__\\/    #
             ##
�#
 #
 #
 #
 #
 #
 #
 ##`