{"version": 3, "file": "network.js", "sourceRoot": "", "sources": ["../../src.ts/providers/network.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,sDAAwD;AACxD,gDAAsE;AAEtE,6DAE8B;AA0B9B;;;;;;;;;;;;;;;;EAgBE;AAGF,MAAM,QAAQ,GAAwC,IAAI,GAAG,EAAE,CAAC;AAGhE;;;GAGG;AACH,MAAa,OAAO;IAChB,KAAK,CAAS;IACd,QAAQ,CAAS;IAEjB,QAAQ,CAA6B;IAErC;;OAEG;IACH,YAAY,IAAY,EAAE,OAAqB;QAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAA,oBAAS,EAAC,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM;QACF,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACH,IAAI,IAAI,KAAa,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACzC,IAAI,IAAI,CAAC,KAAa,IAAI,IAAI,CAAC,KAAK,GAAI,KAAK,CAAC,CAAC,CAAC;IAEhD;;OAEG;IACH,IAAI,OAAO,KAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/C,IAAI,OAAO,CAAC,KAAmB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAA,oBAAS,EAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;IAEjF;;;;;;OAMG;IACH,OAAO,CAAC,KAAiB;QACrB,IAAI,KAAK,IAAI,IAAI,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QAEpC,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YAC5B,IAAI;gBACA,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK,IAAA,oBAAS,EAAC,KAAK,CAAC,CAAC,CAAC;aAC9C;YAAC,OAAO,KAAK,EAAE,GAAG;YACnB,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;SAChC;QAED,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YAC1D,IAAI;gBACA,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK,IAAA,oBAAS,EAAC,KAAK,CAAC,CAAC,CAAC;aAC9C;YAAC,OAAO,KAAK,EAAE,GAAG;YACnB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YAC5B,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,EAAE;gBACvB,IAAI;oBACA,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK,IAAA,oBAAS,EAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;iBACtD;gBAAC,OAAO,KAAK,EAAE,GAAG;gBACnB,OAAO,KAAK,CAAC;aAChB;YACD,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;gBACpB,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC;aACrC;YACD,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACP,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,MAAqB;QAC9B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,mCAAoC,MAAM,CAAC,IAAK,GAAG,CAAC,CAAC;SACxE;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,SAAS,CAA0C,IAAY;QAC3D,OAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,UAAU,CAA0C,QAAgB;QAChE,OAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACH,KAAK;QACD,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC5B,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,mBAAmB,CAAC,EAAmB;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAgB,oCAAoC,CAAC,IAAI,CAAC,IAAI,kCAAa,EAAE,CAAC,CAAC;QAE3G,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,IAAI,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE;YAAE,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC;SAAE;QAC7C,IAAI,EAAE,CAAC,IAAI,EAAE;YACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;gBACxC,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;oBACtC,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC;iBAC3B;qBAAM;oBACH,GAAG,IAAI,KAAK,CAAC,aAAa,CAAC;iBAC9B;aACJ;SACJ;QAED,IAAI,EAAE,CAAC,UAAU,EAAE;YACf,MAAM,UAAU,GAAG,IAAA,wBAAa,EAAC,EAAE,CAAC,UAAU,CAAC,CAAC;YAChD,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;gBAC3B,GAAG,IAAI,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,sBAAsB,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;aACzG;SACJ;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAI,CAAC,OAAoB;QAC5B,oBAAoB,EAAE,CAAC;QAEvB,kBAAkB;QAClB,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAAE;QAExD,6BAA6B;QAC7B,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;YAAE,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;SAAE;QAChE,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC9D,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1C,IAAI,WAAW,EAAE;gBAAE,OAAO,WAAW,EAAE,CAAC;aAAE;YAC1C,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;gBAC9B,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;aAC1C;YAED,IAAA,yBAAc,EAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SAChE;QAED,uCAAuC;QACvC,IAAI,OAAM,CAAW,OAAQ,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE;YACjD,MAAM,KAAK,GAAa,OAAQ,CAAC,KAAK,EAAE,CAAC;YACzC,kFAAkF;YAClF,GAAG;YACH,OAAO,KAAK,CAAC;SAChB;QAED,aAAa;QACb,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC9B,IAAA,yBAAc,EAAC,OAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,EACpF,wCAAwC,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YAElE,MAAM,MAAM,GAAG,IAAI,OAAO,CAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YAE9E,IAAU,OAAQ,CAAC,UAAU,IAAU,OAAQ,CAAC,UAAU,IAAI,IAAI,EAAE;gBAChE,MAAM,CAAC,YAAY,CAAC,IAAI,8BAAS,CAAO,OAAQ,CAAC,UAAU,EAAQ,OAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;aAC5F;YAED,0CAA0C;YAC1C,2FAA2F;YAC3F,GAAG;YAEH,OAAO,MAAM,CAAC;SACjB;QAED,IAAA,yBAAc,EAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,QAAQ,CAAC,aAAuC,EAAE,WAA0B;QAC/E,IAAI,OAAM,CAAC,aAAa,CAAC,KAAK,QAAQ,EAAE;YAAE,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;SAAE;QAClF,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC7C,IAAI,QAAQ,EAAE;YACV,IAAA,yBAAc,EAAC,KAAK,EAAE,2BAA4B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAE,EAAE,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;SACvH;QACD,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IAC7C,CAAC;CACJ;AAzND,0BAyNC;AASD,gEAAgE;AAChE,4DAA4D;AAC5D,8DAA8D;AAC9D,gEAAgE;AAChE,SAAS,UAAU,CAAC,MAAuB,EAAE,QAAgB;IACzD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,uBAAwB,MAAO,EAAE,CAAC,CAAC;KACtD;IAED,iCAAiC;IACjC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAAE;IAE3C,6DAA6D;IAC7D,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,MAAM,IAAI,KAAK,CAAC,uBAAwB,MAAO,EAAE,CAAC,CAAC;KACtD;IAED,sCAAsC;IACtC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,QAAQ,EAAE;QAAE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;KAAE;IAEvD,+DAA+D;IAC/D,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QACrB,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YAAE,IAAI,EAAE,CAAC;SAAE;QACrD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;KAC9B;IAED,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,oDAAoD;AACpD,SAAS,mBAAmB,CAAC,GAAW;IACpC,OAAO,IAAI,iDAA4B,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;QAEnF,0DAA0D;QAC1D,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QAE1C,IAAI,QAAQ,CAAC;QACb,IAAI;YACA,MAAM,CAAE,SAAS,EAAE,QAAQ,CAAE,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC9C,OAAO,CAAC,IAAI,EAAE,EAAE,YAAY,EAAE;aACjC,CAAC,CAAC;YACH,QAAQ,GAAG,SAAS,CAAC;YACrB,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3C,MAAM,OAAO,GAAG;gBACZ,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC3C,oBAAoB,EAAE,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC;aAC9D,CAAC;YACF,OAAO,OAAO,CAAC;SAClB;QAAC,OAAO,KAAU,EAAE;YACjB,IAAA,iBAAM,EAAC,KAAK,EAAE,+CAAgD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAE,GAAG,EAAE,cAAc,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;SAChJ;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,6BAA6B;AAC7B,IAAI,QAAQ,GAAG,KAAK,CAAC;AACrB,SAAS,oBAAoB;IACzB,IAAI,QAAQ,EAAE;QAAE,OAAO;KAAE;IACzB,QAAQ,GAAG,IAAI,CAAC;IAEhB,sCAAsC;IACtC,SAAS,WAAW,CAAC,IAAY,EAAE,OAAe,EAAE,OAAgB;QAChE,MAAM,IAAI,GAAG;YACT,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAE3C,0BAA0B;YAC1B,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE;gBAC5B,OAAO,CAAC,YAAY,CAAC,IAAI,8BAAS,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aACjE;YAED,OAAO,CAAC,YAAY,CAAC,IAAI,kCAAa,EAAE,CAAC,CAAC;YAE1C,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACvC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;QAEF,4CAA4C;QAC5C,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7B,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEhC,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC9B,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAE,WAAW,CAAE,EAAE,CAAC,CAAC;IACxE,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,WAAW,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7C,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC3D,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;IAErD,WAAW,CAAC,SAAS,EAAE,EAAE,EAAE,EAAG,CAAC,CAAC;IAChC,WAAW,CAAC,cAAc,EAAE,CAAC,EAAE,EAAG,CAAC,CAAC;IAEpC,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE;QAC3B,UAAU,EAAE,CAAC;KAChB,CAAC,CAAC;IACH,WAAW,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAG,CAAC,CAAC;IAC5C,WAAW,CAAC,kBAAkB,EAAE,MAAM,EAAE,EAAG,CAAC,CAAC;IAE7C,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,EAAG,CAAC,CAAC;IACvC,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE,EAAG,CAAC,CAAC;IAExC,WAAW,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1C,WAAW,CAAC,MAAM,EAAE,EAAE,EAAE,EAAG,CAAC,CAAC;IAE7B,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/C,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE,EAAG,CAAC,CAAC;IACxC,WAAW,CAAC,eAAe,EAAE,KAAK,EAAE,EAAG,CAAC,CAAC;IAEzC,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE;QACtB,UAAU,EAAE,CAAC;QACb,OAAO,EAAE;YACL,mBAAmB,CAAC,2CAA2C,CAAC;SACnE;KACJ,CAAC,CAAC;IACH,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,EAAG,CAAC,CAAC;IACtC,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE;QAC/B,QAAQ,EAAE,CAAE,aAAa,EAAE,UAAU,CAAE;QACvC,OAAO,EAAE;YACL,mBAAmB,CAAC,mDAAmD,CAAC;SAC3E;KACJ,CAAC,CAAC;IAEH,WAAW,CAAC,UAAU,EAAE,EAAE,EAAE;QACxB,UAAU,EAAE,CAAC;QACb,OAAO,EAAE,EAAG;KACf,CAAC,CAAC;IACH,WAAW,CAAC,iBAAiB,EAAE,GAAG,EAAE,EAAG,CAAC,CAAC;IACzC,WAAW,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAG,CAAC,CAAC;IAE/C,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;AAChD,CAAC"}