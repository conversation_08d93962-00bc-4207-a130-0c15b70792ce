{"version": 3, "file": "signer.d.ts", "sourceRoot": "", "sources": ["../../src.ts/providers/signer.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACrE,OAAO,KAAK,EACR,oBAAoB,EAAE,eAAe,EAAE,cAAc,EACxD,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAE9E,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AACrD,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAEjG;;;;;;;GAOG;AACH,MAAM,WAAW,MAAO,SAAQ,WAAW,EAAE,cAAc,EAAE,YAAY;IAErE;;OAEG;IACH,QAAQ,EAAE,IAAI,GAAG,QAAQ,CAAC;IAE1B;;;OAGG;IACH,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG,QAAQ,GAAG,MAAM,CAAC;IAM3C;;OAEG;IACH,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;IAE9B;;;;;OAKG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAM/C;;;;;;OAMG;IACH,YAAY,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;IAEvE;;;;;;;;;;;;;;;;OAgBG;IACH,mBAAmB,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;IAM9E;;;;;;;;;;;;;;OAcG;IACH,WAAW,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAErD;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE9C;;OAEG;IACH,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;IAMlD;;;OAGG;IACH,eAAe,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEzD;;;;OAIG;IACH,eAAe,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEtE;;;;;;;;;OASG;IACH,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE3D;;OAEG;IACH,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElI;;;;;;OAMG;IACH,qBAAqB,CAAC,IAAI,EAAE,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEjF;;;OAGG;IACH,SAAS,CAAC,aAAa,EAAE,oBAAoB,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;CAC1E"}