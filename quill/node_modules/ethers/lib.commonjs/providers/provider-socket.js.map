{"version": 3, "file": "provider-socket.js", "sourceRoot": "", "sources": ["../../src.ts/providers/provider-socket.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;AAEH,iEAA6D;AAC7D,gDAAsE;AACtE,+DAA2D;AAkB3D;;;GAGG;AACH,MAAa,gBAAgB;IACzB,SAAS,CAAiB;IAE1B,OAAO,CAAS;IAEhB;;OAEG;IACH,IAAI,MAAM,KAAiB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE7D,SAAS,CAAiC;IAC1C,OAAO,CAAiB;IAExB,YAAY,CAAuB;IAEnC;;;OAGG;IACH,YAAY,QAAwB,EAAE,MAAkB;QACpD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED,KAAK;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YAAE,CAAC;YACpF,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACzC,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAI;QACkB,CAAC,IAAI,CAAC,SAAS,CAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YAClD,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;gBAAE,OAAO;aAAE;YACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAE,QAAQ,CAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,qFAAqF;IACrF,oBAAoB;IACpB,KAAK,CAAC,eAAyB;QAC3B,IAAA,iBAAM,EAAC,eAAe,EAAE,kEAAkE,EACtF,uBAAuB,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,eAAe,CAAC;IACrC,CAAC;IAED,MAAM;QACF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAY;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;YAAE,OAAO;SAAE;QACvC,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;YACvB,IAAI,WAAW,GAAyB,IAAI,CAAC,YAAY,CAAC;YAC1D,IAAI,WAAW,IAAI,IAAI,EAAE;gBACrB,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;aACrD;iBAAM;gBACH,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;oBACtC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;aACN;YACD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE;gBACtC,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW,EAAE;oBACnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;iBAC5B;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK,CAAC,QAAwB,EAAE,OAAY;QAC9C,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;IAC/D,CAAC;CACJ;AAnFD,4CAmFC;AAED;;;GAGG;AACH,MAAa,qBAAsB,SAAQ,gBAAgB;IACvD;;OAEG;IACH,YAAY,QAAwB;QAChC,KAAK,CAAC,QAAQ,EAAE,CAAE,UAAU,CAAE,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAwB,EAAE,OAAY;QAC9C,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACrD,CAAC;CACJ;AAXD,sDAWC;AAED;;;GAGG;AACH,MAAa,uBAAwB,SAAQ,gBAAgB;IAEzD;;OAEG;IACH,YAAY,QAAwB;QAChC,KAAK,CAAC,QAAQ,EAAE,CAAE,wBAAwB,CAAE,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAwB,EAAE,OAAY;QAC9C,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;CACJ;AAZD,0DAYC;AAED;;GAEG;AACH,MAAa,qBAAsB,SAAQ,gBAAgB;IACvD,UAAU,CAAS;IAEnB;;OAEG;IACH,IAAI,SAAS,KAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpE;;OAEG;IACH,YAAY,QAAwB,EAAE,MAAmB;QACrD,KAAK,CAAC,QAAQ,EAAE,CAAE,MAAM,EAAE,MAAM,CAAE,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAwB,EAAE,OAAY;QAC9C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IACjF,CAAC;CACJ;AAnBD,sDAmBC;AAED;;;;GAIG;AACH,MAAa,cAAe,SAAQ,wCAAkB;IAClD,UAAU,CAAkG;IAE5G,uCAAuC;IACvC,KAAK,CAAyC;IAE9C,yDAAyD;IACzD,0BAA0B;IAC1B,QAAQ,CAAmC;IAE3C;;;;OAIG;IACH,YAAY,OAAoB,EAAE,QAAoC;QAClE,mBAAmB;QACnB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAG,EAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA,CAAC,CAAC,EAAG,CAAC,CAAC;QAEvE,qDAAqD;QACrD,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAA,yBAAc,EAAC,OAAO,CAAC,aAAa,IAAI,IAAI,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,EACvE,gDAAgD,EAAE,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QACzF,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;QAE1B,kEAAkE;QAClE,mEAAmE;QACnE,gCAAgC;QAChC,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE;YAAE,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;SAAE;QAEpE,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED,wDAAwD;IACxD;;;;;;;MAOE;IAEF,cAAc,CAAC,GAAiB;QAC5B,QAAQ,GAAG,CAAC,IAAI,EAAE;YACd,KAAK,OAAO;gBACR,OAAO,IAAI,0CAAmB,CAAC,OAAO,CAAC,CAAC;YAC5C,KAAK,OAAO;gBACR,OAAO,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,SAAS;gBACV,OAAO,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC7C,KAAK,OAAO;gBACR,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;YACvD,KAAK,QAAQ;gBACT,iDAAiD;gBACjD,8BAA8B;gBAC9B,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;oBAClC,OAAO,IAAI,0CAAmB,CAAC,UAAU,CAAC,CAAC;iBAC9C;SACR;QACD,OAAO,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,QAAyB,EAAE,UAA4B;QAC7D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE;YACT,KAAK,MAAM,OAAO,IAAI,OAAO,EAAE;gBAC3B,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aACtC;YACD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SAClC;IACL,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,OAA+C;QACvD,4CAA4C;QAC5C,IAAA,yBAAc,EAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,uCAAuC,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAErG,gEAAgE;QAEhE,kCAAkC;QAClC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC5C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,0DAA0D;QAC1D,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAE7B,kCAAkC;QAClC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAE3C,OAA4C,CAAE,MAAM,OAAO,CAAE,CAAC;IAClE,CAAC;IAED,qDAAqD;IACrD;;;;;;;;;;;;MAYE;IAEF;;;OAGG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe;QACjC,MAAM,MAAM,GAAuD,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAEzF,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,IAAI,IAAI,MAAM,EAAE;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChD,IAAI,QAAQ,IAAI,IAAI,EAAE;gBAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAA,oBAAS,EAAC,gCAAgC,EAAE,eAAe,EAAE;oBAC5E,UAAU,EAAE,YAAY;oBACxB,MAAM;iBACT,CAAC,CAAC,CAAC;gBACJ,OAAO;aACV;YACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAElC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAE5B;aAAM,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,kBAAkB,EAAE;YACvD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,UAAU,EAAE;gBACZ,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACnD;iBAAM;gBACH,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC1C,IAAI,OAAO,IAAI,IAAI,EAAE;oBACjB,OAAO,GAAG,EAAG,CAAC;oBACd,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;iBACxC;gBACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACtC;SAEJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAA,oBAAS,EAAC,6BAA6B,EAAE,eAAe,EAAE;gBACzE,UAAU,EAAE,oBAAoB;gBAChC,MAAM;aACT,CAAC,CAAC,CAAC;YACJ,OAAO;SACV;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,MAAM,CAAC,OAAe;QACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;CACJ;AAvKD,wCAuKC"}