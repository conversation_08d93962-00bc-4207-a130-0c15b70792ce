{"version": 3, "file": "namehash.js", "sourceRoot": "", "sources": ["../../src.ts/hash/namehash.ts"], "names": [], "mappings": ";;;AACA,iDAA+C;AAC/C,gDAE2B;AAG3B,0DAAuD;AAEvD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACjC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAEd,SAAS,cAAc,CAAC,IAAgB;IACpC,IAAA,yBAAc,EAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,mCAAmC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IACpF,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,IAAY;IAC9B,MAAM,KAAK,GAAG,IAAA,sBAAW,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9C,MAAM,KAAK,GAAsB,EAAG,CAAC;IAErC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAAE,OAAO,KAAK,CAAC;KAAE;IAExC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEnB,8CAA8C;QAC9C,IAAI,CAAC,KAAK,IAAI,EAAE;YACZ,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;SAChB;KACJ;IAED,qDAAqD;IACrD,IAAA,yBAAc,EAAC,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,mCAAmC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAEvF,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,IAAY;IACrC,IAAI;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;SAAE;QAC1D,OAAO,IAAA,6BAAa,EAAC,IAAI,CAAC,CAAC;KAC9B;IAAC,OAAO,KAAU,EAAE;QACjB,IAAA,yBAAc,EAAC,KAAK,EAAE,qBAAsB,KAAK,CAAC,OAAQ,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KAChF;AACL,CAAC;AAPD,oCAOC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,IAAY;IACpC,IAAI;QACA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;KAC5C;IAAC,OAAO,KAAK,EAAE,GAAG;IACnB,OAAO,KAAK,CAAC;AACjB,CAAC;AALD,kCAKC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,IAAY;IACjC,IAAA,yBAAc,EAAC,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,gCAAgC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAE1F,IAAA,yBAAc,EAAC,IAAI,CAAC,MAAM,EAAE,gCAAgC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAE5E,IAAI,MAAM,GAAwB,KAAK,CAAC;IAExC,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACjC,OAAO,KAAK,CAAC,MAAM,EAAE;QACjB,MAAM,GAAG,IAAA,oBAAS,EAAC,IAAA,iBAAM,EAAC,CAAE,MAAM,EAAE,IAAA,oBAAS,EAAa,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;KAChF;IAED,OAAO,IAAA,kBAAO,EAAC,MAAM,CAAC,CAAC;AAC3B,CAAC;AAbD,4BAaC;AAED;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,IAAY,EAAE,UAAmB;IACvD,MAAM,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA,CAAC,CAAC,EAAE,CAAC;IAErD,IAAA,yBAAc,EAAC,MAAM,IAAI,GAAG,EAAE,qCAAqC,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAEvF,OAAO,IAAA,kBAAO,EAAC,IAAA,iBAAM,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAClD,IAAA,yBAAc,EAAC,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE,SAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAE,YAAa,MAAO,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAEjH,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9C,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACnB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,OAAO,KAAK,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAChB,CAAC;AAbD,8BAaC"}