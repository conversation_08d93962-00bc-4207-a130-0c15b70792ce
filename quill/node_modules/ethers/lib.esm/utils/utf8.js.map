{"version": 3, "file": "utf8.js", "sourceRoot": "", "sources": ["../../src.ts/utils/utf8.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AACH,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AACrC,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAuE9D,SAAS,SAAS,CAAC,MAAuB,EAAE,MAAc,EAAE,KAAiB,EAAE,MAAqB,EAAE,YAAqB;IACvH,cAAc,CAAC,KAAK,EAAE,+BAAgC,MAAO,KAAM,MAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAClG,CAAC;AAED,SAAS,UAAU,CAAC,MAAuB,EAAE,MAAc,EAAE,KAAiB,EAAE,MAAqB,EAAE,YAAqB;IAExH,uGAAuG;IACvG,IAAI,MAAM,KAAK,YAAY,IAAI,MAAM,KAAK,qBAAqB,EAAE;QAC7D,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;gBAAE,MAAM;aAAE;YACtC,CAAC,EAAE,CAAC;SACP;QACD,OAAO,CAAC,CAAC;KACZ;IAED,wEAAwE;IACxE,mEAAmE;IACnE,IAAI,MAAM,KAAK,SAAS,EAAE;QACtB,OAAO,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;KACpC;IAED,kBAAkB;IAClB,OAAO,CAAC,CAAC;AACb,CAAC;AAED,SAAS,WAAW,CAAC,MAAuB,EAAE,MAAc,EAAE,KAAiB,EAAE,MAAqB,EAAE,YAAqB;IAEzH,sFAAsF;IACtF,IAAI,MAAM,KAAK,UAAU,EAAE;QACvB,cAAc,CAAC,OAAM,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE,wCAAwC,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;QAC1H,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,CAAC,CAAC;KACZ;IAED,gDAAgD;IAChD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAEpB,2CAA2C;IAC3C,OAAO,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;AACnE,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,MAAM,cAAc,GAAoE,MAAM,CAAC,MAAM,CAAC;IACzG,KAAK,EAAE,SAAS;IAChB,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,WAAW;CACvB,CAAC,CAAC;AAEH,oFAAoF;AACpF,SAAS,iBAAiB,CAAC,MAAiB,EAAE,OAAuB;IACjE,IAAI,OAAO,IAAI,IAAI,EAAE;QAAE,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC;KAAE;IAExD,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAExC,MAAM,MAAM,GAAkB,EAAE,CAAC;IACjC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,4BAA4B;IAC5B,OAAM,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE;QAEpB,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAErB,YAAY;QACZ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACd,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACf,SAAS;SACZ;QAED,qDAAqD;QACrD,IAAI,WAAW,GAAkB,IAAI,CAAC;QACtC,IAAI,YAAY,GAAkB,IAAI,CAAC;QAEvC,sBAAsB;QACtB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YACrB,WAAW,GAAG,CAAC,CAAC;YAChB,YAAY,GAAG,IAAI,CAAC;YAExB,gCAAgC;SAC/B;aAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YAC5B,WAAW,GAAG,CAAC,CAAC;YAChB,YAAY,GAAG,KAAK,CAAC;YAEzB,0CAA0C;SACzC;aAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YAC5B,WAAW,GAAG,CAAC,CAAC;YAChB,YAAY,GAAG,MAAM,CAAC;SAEzB;aAAM;YACH,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;gBACrB,CAAC,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aAC7D;iBAAM;gBACH,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aACpD;YACD,SAAS;SACZ;QAED,uCAAuC;QACvC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,IAAI,KAAK,CAAC,MAAM,EAAE;YACrC,CAAC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC9C,SAAS;SACZ;QAED,yCAAyC;QACzC,IAAI,GAAG,GAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEhE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAExB,4BAA4B;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE;gBAC3B,CAAC,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBACnD,GAAG,GAAG,IAAI,CAAC;gBACX,MAAM;aACT;YAAA,CAAC;YAEF,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;YACrC,CAAC,EAAE,CAAC;SACP;QAED,+CAA+C;QAC/C,IAAI,GAAG,KAAK,IAAI,EAAE;YAAE,SAAS;SAAE;QAE/B,qBAAqB;QACrB,IAAI,GAAG,GAAG,QAAQ,EAAE;YAChB,CAAC,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACtE,SAAS;SACZ;QAED,uCAAuC;QACvC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,EAAE;YAChC,CAAC,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACzE,SAAS;SACZ;QAED,wDAAwD;QACxD,IAAI,GAAG,IAAI,YAAY,EAAE;YACrB,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YAClE,SAAS;SACZ;QAED,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACpB;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,uFAAuF;AAEvF;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,GAAW,EAAE,IAA+B;IACpE,cAAc,CAAC,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,sBAAsB,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAE7E,IAAI,IAAI,IAAI,IAAI,EAAE;QACd,eAAe,CAAC,IAAI,CAAC,CAAC;QACtB,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;IAED,IAAI,MAAM,GAAkB,EAAE,CAAC;IAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,MAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAE5B,IAAI,CAAC,GAAG,IAAI,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAElB;aAAM,IAAI,CAAC,GAAG,KAAK,EAAE;YAClB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAElC;aAAM,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,MAAM,EAAE;YAC/B,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAE7B,cAAc,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,EACvD,wBAAwB,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YAE1C,iBAAiB;YACjB,MAAM,IAAI,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAErC;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAClC;KACJ;IAED,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAAA,CAAC;AAEF,SAAS;AACT,SAAS,aAAa,CAAC,UAAyB;IAC5C,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;QAChC,IAAI,SAAS,IAAI,MAAM,EAAE;YACrB,OAAO,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;SACzC;QACD,SAAS,IAAI,OAAO,CAAC;QACrB,OAAO,MAAM,CAAC,YAAY,CACtB,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,EACtC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CACjC,CAAC;IACN,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,YAAY,CAAC,KAAgB,EAAE,OAAuB;IAClE,OAAO,aAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAAC,GAAW,EAAE,IAA+B;IACzE,OAAO,iBAAiB,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACrD,CAAC"}