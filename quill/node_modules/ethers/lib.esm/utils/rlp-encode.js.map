{"version": 3, "file": "rlp-encode.js", "sourceRoot": "", "sources": ["../../src.ts/utils/rlp-encode.ts"], "names": [], "mappings": "AAAA,gDAAgD;AAEhD,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AAKrC,SAAS,eAAe,CAAC,KAAa;IAClC,MAAM,MAAM,GAAkB,EAAE,CAAC;IACjC,OAAO,KAAK,EAAE;QACV,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAC7B,KAAK,KAAK,CAAC,CAAC;KACf;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,OAAO,CAAC,MAAwC;IACrD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,OAAO,GAAkB,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,CAAC,UAAS,KAAK;YACzB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE;YACtB,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;YACtC,OAAO,OAAO,CAAC;SAClB;QAED,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAErC,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAEjC;IAED,MAAM,IAAI,GAAkB,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEnF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QACtC,OAAO,IAAI,CAAC;KAEf;SAAM,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;KACf;IAED,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAErC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC;AAED,MAAM,OAAO,GAAG,kBAAkB,CAAC;AAEnC;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,MAA4B;IAClD,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;QAC7B,MAAM,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1B,MAAM,IAAI,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;KAC9B;IACD,OAAO,MAAM,CAAC;AAClB,CAAC"}