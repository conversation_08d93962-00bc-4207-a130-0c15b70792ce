{"version": 3, "file": "mnemonic.js", "sourceRoot": "", "sources": ["../../src.ts/wallet/mnemonic.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EACH,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EACnG,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAMjD,uCAAuC;AACvC,SAAS,YAAY,CAAC,IAAY;IAC/B,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AACjD,CAAC;AAED,uCAAuC;AACvC,SAAS,YAAY,CAAC,IAAY;IAC/B,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AACnC,CAAC;AAGD,SAAS,iBAAiB,CAAC,QAAgB,EAAE,QAA0B;IACnE,eAAe,CAAC,MAAM,CAAC,CAAC;IAExB,IAAI,QAAQ,IAAI,IAAI,EAAE;QAAE,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;KAAE;IAEvD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACvC,cAAc,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,EAC/E,yBAAyB,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IAE3D,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAEjE,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,cAAc,CAAC,KAAK,IAAI,CAAC,EAAE,kCAAmC,CAAE,EAAE,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;QAEhG,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;YAC/B,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;gBAC3B,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACrD;YACD,MAAM,EAAE,CAAC;SACZ;KACJ;IAED,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAG1C,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IACtC,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;IAEhD,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;IAEvF,cAAc,CAAC,QAAQ,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,EACpE,2BAA2B,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IAE7D,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAmB,EAAE,QAA0B;IAEtE,cAAc,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,IAAI,EAAE,IAAI,OAAO,CAAC,MAAM,IAAI,EAAE,EACrF,sBAAsB,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;IAEvD,IAAI,QAAQ,IAAI,IAAI,EAAE;QAAE,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;KAAE;IAEvD,MAAM,OAAO,GAAkB,CAAE,CAAC,CAAE,CAAC;IAErC,IAAI,aAAa,GAAG,EAAE,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAErC,iDAAiD;QACjD,IAAI,aAAa,GAAG,CAAC,EAAE;YACnB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAClC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;YAE1C,aAAa,IAAI,CAAC,CAAC;YAEvB,0CAA0C;SACzC;aAAM;YACH,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,aAAa,CAAC;YAC9C,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;YAEjE,sBAAsB;YACtB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;YAE3D,aAAa,IAAI,CAAC,CAAC;SACtB;KACJ;IAED,4BAA4B;IAC5B,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;IAE5F,2CAA2C;IAC3C,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC;IAC7C,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;IAEhE,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAY,QAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC;AAED,MAAM,MAAM,GAAG,EAAG,CAAC;AAEnB;;;GAGG;AACH,MAAM,OAAO,QAAQ;IACjB;;;;OAIG;IACM,MAAM,CAAU;IAEzB;;;OAGG;IACM,QAAQ,CAAU;IAE3B;;OAEG;IACM,QAAQ,CAAY;IAE7B;;OAEG;IACM,OAAO,CAAU;IAE1B;;OAEG;IACH,YAAY,KAAU,EAAE,OAAe,EAAE,MAAc,EAAE,QAAwB,EAAE,QAA0B;QACzG,IAAI,QAAQ,IAAI,IAAI,EAAE;YAAE,QAAQ,GAAG,EAAE,CAAC;SAAE;QACxC,IAAI,QAAQ,IAAI,IAAI,EAAE;YAAE,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SAAE;QACvD,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QACzC,gBAAgB,CAAW,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,WAAW;QACP,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7D,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,QAAwB,EAAE,QAA0B;QAClF,kDAAkD;QAClD,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACpD,MAAM,GAAG,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC;QACxD,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,WAAW,CAAC,QAAmB,EAAE,QAAwB,EAAE,QAA0B;QACxF,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAAmB,EAAE,QAA0B;QAClE,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC9C,OAAO,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,MAAc,EAAE,QAA0B;QAC7D,OAAO,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,eAAe,CAAC,MAAc,EAAE,QAA0B;QAC7D,IAAI;YACA,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACpC,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,KAAK,EAAE,GAAG;QACnB,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ"}