{"version": 3, "file": "provider-websocket.js", "sourceRoot": "", "sources": ["../../src.ts/providers/provider-websocket.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,IAAI,UAAU,EAAE,MAAM,SAAS,CAAC,CAAC,YAAY;AAE/D,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAyBtD;;;;;;;;;GASG;AACH,MAAM,OAAO,iBAAkB,SAAQ,cAAc;IACjD,QAAQ,CAA0B;IAElC,UAAU,CAAuB;IACjC,IAAI,SAAS;QACT,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SAAE;QACrE,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,YAAY,GAA8C,EAAE,OAAoB,EAAE,OAAmC;QACjH,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxB,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YAC1B,IAAI,CAAC,QAAQ,GAAG,GAAG,EAAE,GAAG,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;SACrC;aAAM,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,UAAU,EAAE;YACnC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;YACpB,IAAI,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;SAC3B;aAAM;YACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;SACzB;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,IAAI,EAAE;YAC/B,IAAI;gBACA,MAAM,IAAI,CAAC,MAAM,EAAE,CAAA;gBACnB,IAAI,CAAC,MAAM,EAAE,CAAC;aACjB;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;gBACxD,sCAAsC;aACzC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,OAAyB,EAAE,EAAE;YACrD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QACV;;;;;;;;;;;;;;UAcE;IACE,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAAe;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YACzB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;CACJ"}