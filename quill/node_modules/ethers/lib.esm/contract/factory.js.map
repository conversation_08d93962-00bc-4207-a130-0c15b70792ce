{"version": 3, "file": "factory.js", "sourceRoot": "", "sources": ["../../src.ts/contract/factory.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EACH,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAC3C,MAAM,EAAE,cAAc,EACzB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAazE,mCAAmC;AACnC,sCAAsC;AAEtC;;GAEG;AACH,MAAM,OAAO,eAAe;IAExB;;OAEG;IACM,SAAS,CAAa;IAE/B;;OAEG;IACM,QAAQ,CAAU;IAE3B;;OAEG;IACM,MAAM,CAAyB;IAExC;;;;;;OAMG;IACH,YAAY,GAA6B,EAAE,QAAwC,EAAE,MAA8B;QAC/G,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAElC,wEAAwE;QACxE,IAAI,QAAQ,YAAY,UAAU,EAAE;YAChC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1C;aAAM;YACH,IAAI,OAAM,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;gBAAE,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;aAAE;YAClE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAAE,QAAQ,GAAG,IAAI,GAAG,QAAQ,CAAC;aAAE;YAC/D,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1C;QAED,gBAAgB,CAAkB,IAAI,EAAE;YACpC,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,MAAM,IAAI,IAAI,CAAC;SACvD,CAAC,CAAC;IACP,CAAC;IAED,MAAM,CAAC,MAA4B;QAC/B,OAAO,IAAU,YAAa,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,oBAAoB,CAAC,GAAG,IAA2B;QACrD,IAAI,SAAS,GAA4C,EAAG,CAAC;QAE7D,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAEvC,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;YAC5C,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;SAC/C;QAED,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;YACxC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;SACnE;QAED,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE3E,MAAM,IAAI,GAAG,MAAM,CAAC,CAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,CAAE,CAAC,CAAC;QAClF,OAAO,MAAM,CAAC,MAAM,CAAC,EAAG,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,MAAM,CAAC,GAAG,IAA2B;QACvC,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,CAAC;QAEpD,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,OAAM,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,UAAU,EACpE,sDAAsD,EAAE,uBAAuB,EAAE;YACjF,SAAS,EAAE,iBAAiB;SAAE,CAAC,CAAC;QAEpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACrD,MAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACzC,OAAO,IAAU,YAAa,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjF,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,MAA6B;QACjC,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAA2D,MAAW,EAAE,MAAuB;QAC9G,cAAc,CAAC,MAAM,IAAI,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAExE,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;YAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAAE;QAEjE,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;QAEvB,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,MAAM,CAAC,QAAQ,EAAE;YACjB,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;SAC9B;aAAM,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC1C,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;SAClC;QAED,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;CACJ"}