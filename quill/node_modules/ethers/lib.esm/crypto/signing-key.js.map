{"version": 3, "file": "signing-key.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/signing-key.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEpD,OAAO,EACH,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAC5D,cAAc,EACjB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAO3C;;;GAGG;AACH,MAAM,OAAO,UAAU;IACnB,WAAW,CAAS;IAEpB;;OAEG;IACH,YAAY,UAAqB;QAC7B,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,qBAAqB,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QACjG,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,UAAU,KAAa,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAErD;;;;;OAKG;IACH,IAAI,SAAS,KAAa,OAAO,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEjF;;;;;;OAMG;IACH,IAAI,mBAAmB,KAAa,OAAO,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAEjG;;OAEG;IACH,IAAI,CAAC,MAAiB;QAClB,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,uBAAuB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErF,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC7E,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC,IAAI,CAAC;YAClB,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;YACrB,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;YACrB,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAA,CAAC,CAAC,IAAI,CAAC;SACjC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,mBAAmB,CAAC,KAAgB;QAChC,MAAM,MAAM,GAAG,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAClD,OAAO,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACvG,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAc,EAAE,UAAoB;QACxD,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAEjC,cAAc;QACd,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;YACrB,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;YAC3D,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;SAC1B;QAED,wDAAwD;QACxD,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;YACrB,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;YAC/B,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YACd,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClB,KAAK,GAAG,GAAG,CAAC;SACf;QAED,MAAM,KAAK,GAAG,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,OAAO,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAiB,EAAE,SAAwB;QAC/D,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,uBAAuB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErF,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAI,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC;QACtF,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,cAAc,CAAC,MAAM,IAAI,IAAI,EAAE,8BAA8B,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAEvF,OAAO,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,SAAS,CAAC,EAAa,EAAE,EAAa,EAAE,UAAoB;QAC/D,MAAM,IAAI,GAAG,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,MAAM,IAAI,GAAG,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;IACpD,CAAC;CACJ"}