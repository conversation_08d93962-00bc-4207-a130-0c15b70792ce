{"version": 3, "file": "signature.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/signature.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EACH,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAC3D,SAAS,EAAE,WAAW,EAAE,YAAY,EACpC,cAAc,EAAE,aAAa,EAChC,MAAM,mBAAmB,CAAC;AAO3B,YAAY;AACZ,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AACzB,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AACzB,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAGzB,MAAM,MAAM,GAAG,EAAG,CAAC;AA6BnB,SAAS,SAAS,CAAC,KAAmB;IAClC,OAAO,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;GAKG;AACH,MAAM,OAAO,SAAS;IAClB,EAAE,CAAS;IACX,EAAE,CAAS;IACX,EAAE,CAAU;IACZ,SAAS,CAAgB;IAEzB;;;;;OAKG;IACH,IAAI,CAAC,KAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC,IAAI,CAAC,CAAC,KAAgB;QAClB,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,KAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC,IAAI,CAAC,CAAC,MAAiB;QACnB,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAC9B,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACvF,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;IACpB,CAAC;IAED;;;;;;;;;OASG;IACH,IAAI,CAAC,KAAc,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC,CAAC,KAAmB;QACrB,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACpC,cAAc,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9D,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,IAAI,QAAQ,KAAoB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAExD;;;OAGG;IACH,IAAI,aAAa;QACb,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QACxB,IAAI,CAAC,IAAI,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAC/B,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACP,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAI,WAAW;QACX,sCAAsC;QACtC,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,OAAO,EAAE;YAAE,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;SAAE;QAC7C,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACjB,OAAO,MAAM,CAAC,CAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAE,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACV,OAAO,MAAM,CAAC,CAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,MAAM,CAAC,CAAE,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,YAAY,KAAU,EAAE,CAAS,EAAE,CAAS,EAAE,CAAU;QACpD,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QACtC,OAAO,mBAAoB,IAAI,CAAC,CAAE,UAAW,IAAI,CAAC,CAAE,eAAgB,IAAI,CAAC,OAAQ,eAAgB,IAAI,CAAC,QAAS,IAAI,CAAC;IACxH,CAAC;IAED;;OAEG;IACH,KAAK;QACD,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,IAAI,CAAC,QAAQ,EAAE;YAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;SAAE;QACvD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,OAAO;YACH,KAAK,EAAE,WAAW;YAClB,QAAQ,EAAE,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAA,CAAC,CAAC,IAAI,CAAC;YAC1D,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SAClC,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,UAAU,CAAC,CAAe;QAC7B,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAE7B,+DAA+D;QAC/D,IAAI,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAEpD,6BAA6B;QAC7B,cAAc,CAAC,EAAE,IAAI,KAAK,EAAE,mBAAmB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEzD,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,WAAW,CAAC,OAAqB,EAAE,CAAU;QAChD,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CAAC,cAAc,CAAC,CAAe;QACjC,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAExB,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE;YAAE,OAAO,EAAE,CAAC;SAAE;QAC/C,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE;YAAE,OAAO,EAAE,CAAC;SAAE;QAE/C,cAAc,CAAC,EAAE,IAAI,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEjD,sDAAsD;QACtD,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,CAAC,CAAC,EAAE,CAAC;IAChC,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,IAAI,CAAC,GAAmB;QAC3B,SAAS,WAAW,CAAC,KAAc,EAAE,OAAe;YAChD,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QACrD,CAAC;QAAA,CAAC;QAEF,IAAI,GAAG,IAAI,IAAI,EAAE;YACb,OAAO,IAAI,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;SACxD;QAED,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YAC1B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YACzC,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;gBACrB,MAAM,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,CAAC,CAAC,EAAE,CAAC;gBACjC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;gBACb,OAAO,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAClD;YAED,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;gBACrB,MAAM,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC9B,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,iBAAiB,CAAC,CAAC;gBACpD,MAAM,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9C,OAAO,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAClD;YAED,WAAW,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;SACtD;QAED,IAAI,GAAG,YAAY,SAAS,EAAE;YAAE,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC;SAAE;QAErD,QAAQ;QACR,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;QACjB,WAAW,CAAC,EAAE,IAAI,IAAI,EAAE,WAAW,CAAC,CAAC;QACrC,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;QAExB,6DAA6D;QAC7D,MAAM,CAAC,GAAG,CAAC,UAAS,CAAU,EAAE,WAAoB;YAChD,IAAI,CAAC,IAAI,IAAI,EAAE;gBAAE,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;aAAE;YAEvC,IAAI,WAAW,IAAI,IAAI,EAAE;gBACrB,WAAW,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,qBAAqB,CAAC,CAAC;gBACjE,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACpC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;gBACjB,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;aACzB;YAED,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC;QAC3B,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAE7D,6DAA6D;QAC7D,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAC,UAAS,EAAiB,EAAE,WAAoB,EAAE,OAAiB;YACxF,IAAI,EAAE,IAAI,IAAI,EAAE;gBACZ,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;gBACxB,OAAO;oBACH,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,CAAC,SAAS,CAAC;oBACvC,CAAC,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC;iBACjC,CAAC;aACL;YAED,IAAI,WAAW,IAAI,IAAI,EAAE;gBACrB,WAAW,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,qBAAqB,CAAC,CAAC;gBACjE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;aAC9D;YAED,IAAI,OAAO,IAAI,IAAI,EAAE;gBACjB,QAAQ,SAAS,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE;oBACvC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;oBACzB,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;iBAC5B;gBACD,WAAW,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;aACzC;YAED,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAExC,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,QAAQ,EAAE;YAAE,MAAM,CAAC,SAAS,GAAI,QAAQ,CAAC;SAAE;QAE/C,oEAAoE;QACpE,WAAW,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,KAAK,MAAM,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACjH,WAAW,CAAC,GAAG,CAAC,WAAW,IAAI,IAAI,IAAI,GAAG,CAAC,WAAW,KAAK,MAAM,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC;QAEvG,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ"}